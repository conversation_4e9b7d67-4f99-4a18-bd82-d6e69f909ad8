// Main process
const { app, BrowserWindow, ipcMain, session, shell } = require("electron")
const path = require("path")
const url = require("url")

// Add a global flag to track if we're restarting
let isRestarting = false

// Keep a global reference of the window object
let mainWindow

// Set up download handler for ALL sessions - SIMPLIFIED VERSION
const setupDownloadHandler = (sess) => {
  if (!sess) return;

  sess.on('will-download', (event, item, webContents) => {
    // Get download whitelist
    const downloadWhitelist = store ? JSON.parse(store.get('settings')?.downloadWhitelist || '[]') : [];
    const blockDownloads = store ? store.get('settings')?.blockDownloads === 'true' : false;

    // Get file info
    const filePath = item.getFilename();
    const fileExt = filePath.split('.').pop().toLowerCase();

    console.log(`Download requested: ${filePath} (${fileExt})`);
    console.log(`Block downloads: ${blockDownloads}`);
    console.log(`Download whitelist: ${JSON.stringify(downloadWhitelist)}`);

    // Check if downloads are blocked and this extension is not whitelisted
    if (blockDownloads && !downloadWhitelist.includes(fileExt)) {
      console.log(`Blocking download of ${filePath} - extension ${fileExt} not in whitelist`);

      // Cancel the download
      item.cancel();

      // Notify the renderer
      if (mainWindow) {
        mainWindow.webContents.send('download-blocked', {
          filename: filePath,
          extension: fileExt
        });
      }

      return;
    }

    // Generate a unique download ID
    const downloadId = Date.now().toString();

    // Show initial download notification
    if (mainWindow) {
      mainWindow.webContents.send('download-started', {
        id: downloadId,
        filename: filePath,
        fileSize: item.getTotalBytes()
      });
    }

    // Set up progress tracking
    item.on('updated', (event, state) => {
      if (!mainWindow) return;

      try {
        if (state === 'interrupted') {
          console.log('Download interrupted');
          mainWindow.webContents.send('download-updated', {
            id: downloadId,
            filename: filePath,
            state: 'interrupted'
          });
        } else if (state === 'progressing') {
          if (item.isPaused()) {
            console.log('Download paused');
            mainWindow.webContents.send('download-updated', {
              id: downloadId,
              filename: filePath,
              state: 'paused'
            });
          } else {
            // Only send progress updates if we have total bytes
            if (item.getTotalBytes() > 0) {
              const progress = Math.round((item.getReceivedBytes() / item.getTotalBytes()) * 100);
              console.log(`Download progress: ${progress}%`);
              mainWindow.webContents.send('download-updated', {
                id: downloadId,
                filename: filePath,
                state: 'progressing',
                progress: progress
              });
            } else {
              // If we don't know total size, show indeterminate progress
              console.log(`Download progressing (unknown size)`);
              mainWindow.webContents.send('download-updated', {
                id: downloadId,
                filename: filePath,
                state: 'progressing',
                progress: -1 // Indicate indeterminate progress
              });
            }
          }
        }
      } catch (e) {
        console.error('Error during download update:', e);
      }
    });

    // Report download completion
    item.once('done', (event, state) => {
      if (!mainWindow) return;

      try {
        if (state === 'completed') {
          console.log(`Download completed: ${filePath}`);
          mainWindow.webContents.send('download-completed', {
            id: downloadId,
            filename: filePath,
            path: item.getSavePath()
          });
        } else {
          console.log(`Download failed: ${state}`);
          mainWindow.webContents.send('download-failed', {
            id: downloadId,
            filename: filePath,
            state: state
          });
        }
      } catch (e) {
        console.error('Error during download completion:', e);
      }
    });
  });
}

// Initialize store for settings and set up session listeners after store is ready
let store

// Helper: parse rules from settings
function parseRules(settings) {
  // Block/allow list: one pattern per line
  const blockList = (settings.blockList || '').split('\n').map(s => s.trim()).filter(Boolean)
  // Time limits: domain: X/Y (e.g. youtube.com: 10/1)
  const timeLimits = {}
  for (const line of (settings.timeLimits || '').split('\n')) {
    const m = line.match(/^(.+?):\s*(\d+)\/(\d+)/)
    if (m) timeLimits[m[1].trim()] = { minutes: parseInt(m[2]), hours: parseInt(m[3]) }
  }
  // Schedules: domain: HH:MM-HH:MM
  const schedules = {}
  for (const line of (settings.schedules || '').split('\n')) {
    const m = line.match(/^(.+?):\s*(\d{2}:\d{2})-(\d{2}:\d{2})/)
    if (m) schedules[m[1].trim()] = { start: m[2], end: m[3] }
  }
  return { blockList, timeLimits, schedules, password: settings.password || '', redirectUrl: settings.redirectUrl || '' }
}

// Helper: match domain against block domain list
function compareDomains(blockDomain, url) {
  try {
    // Skip empty inputs
    if (!blockDomain || !url) {
      console.log('Empty domain or URL, skipping comparison');
      return false;
    }

    // Normalize inputs
    blockDomain = blockDomain.toLowerCase().trim();

    // Extract domain from URL
    let urlDomain = '';
    try {
      const urlObj = new URL(url);
      urlDomain = urlObj.hostname.toLowerCase();
    } catch (e) {
      console.log('Error extracting domain from URL:', e);
      // Try regex extraction as fallback
      const pattern = /[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,6}(:[0-9]{1,5})?/g;
      const match = url.match(pattern);
      urlDomain = match ? match[0].toLowerCase() : '';
    }

    if (!urlDomain) {
      console.log('Could not extract domain from URL:', url);
      return false;
    }

    console.log(`Comparing block domain "${blockDomain}" with URL domain "${urlDomain}"`);

    // Exact match
    if (blockDomain === urlDomain) {
      console.log(`Exact domain match: ${blockDomain} === ${urlDomain}`);
      return true;
    }

    // Match with/without www prefix
    if (blockDomain === urlDomain.replace(/^www\./, '') ||
        'www.' + blockDomain === urlDomain) {
      console.log(`Domain match with www handling: ${blockDomain} matches ${urlDomain}`);
      return true;
    }

    // Subdomain match (e.g., block example.com should also block sub.example.com)
    if (urlDomain.endsWith('.' + blockDomain)) {
      console.log(`Subdomain match: ${urlDomain} is a subdomain of ${blockDomain}`);
      return true;
    }

    // No match
    return false;
  } catch (e) {
    console.error('Error in domain comparison:', e);
    return false;
  }
}

// Helper: match URL against block URL list
function compareURLS(blockedUrl, tabUrl) {
  try {
    // Skip empty inputs
    if (!blockedUrl || !tabUrl) {
      console.log('Empty blocked URL or tab URL, skipping comparison');
      return false;
    }

    // Normalize inputs
    blockedUrl = blockedUrl.toLowerCase().trim();
    tabUrl = tabUrl.toLowerCase().trim();

    // Skip browser internal pages
    if (tabUrl.includes("chrome-extension://") ||
        tabUrl.includes("chrome://") ||
        tabUrl.includes("about:") ||
        tabUrl.includes("file://") ||
        tabUrl.includes("data:") ||
        tabUrl.includes("javascript:")) {
      console.log('Browser internal page, skipping URL check');
      return false;
    }

    console.log(`Comparing blocked URL "${blockedUrl}" with tab URL "${tabUrl}"`);

    // Check for wildcard patterns (e.g., *.example.com)
    if (blockedUrl.startsWith('*.')) {
      const domain = blockedUrl.substring(2); // Remove *. prefix
      try {
        const urlObj = new URL(tabUrl);
        const hostname = urlObj.hostname.toLowerCase();

        if (hostname === domain || hostname.endsWith('.' + domain)) {
          console.log(`Wildcard match: ${hostname} matches pattern ${blockedUrl}`);
          return true;
        }
      } catch (e) {
        console.log('Error parsing URL for wildcard check:', e);
      }
    }

    // Check for regex patterns (e.g., /.*ads.*/)
    if (blockedUrl.startsWith('/') && blockedUrl.endsWith('/')) {
      try {
        const regexPattern = blockedUrl.substring(1, blockedUrl.length - 1);
        const regex = new RegExp(regexPattern, 'i');

        if (regex.test(tabUrl)) {
          console.log(`Regex match: ${tabUrl} matches pattern ${blockedUrl}`);
          return true;
        }
      } catch (e) {
        console.log('Error with regex pattern:', e);
      }
    }

    // Extract URL parts for comparison
    let blockedUrlClean = '';
    let tabUrlClean = '';

    try {
      // Try to parse as full URLs
      const blockedUrlObj = new URL(blockedUrl.startsWith('http') ? blockedUrl : 'http://' + blockedUrl);
      const tabUrlObj = new URL(tabUrl);

      // Get hostname + path for comparison
      blockedUrlClean = blockedUrlObj.hostname + blockedUrlObj.pathname;
      tabUrlClean = tabUrlObj.hostname + tabUrlObj.pathname;

      // Remove trailing slashes
      blockedUrlClean = blockedUrlClean.replace(/\/$/, '');
      tabUrlClean = tabUrlClean.replace(/\/$/, '');

      // Handle www prefix
      const blockedWithoutWww = blockedUrlClean.replace(/^www\./, '');
      const tabWithoutWww = tabUrlClean.replace(/^www\./, '');

      // Exact match (with or without www)
      if (blockedUrlClean === tabUrlClean ||
          blockedWithoutWww === tabWithoutWww ||
          'www.' + blockedWithoutWww === tabUrlClean ||
          blockedUrlClean === 'www.' + tabWithoutWww) {
        console.log(`Exact URL match: ${blockedUrlClean} matches ${tabUrlClean}`);
        return true;
      }

      // Check if tab URL starts with blocked URL (path prefix match)
      if (tabUrlClean.startsWith(blockedUrlClean) ||
          tabWithoutWww.startsWith(blockedWithoutWww)) {
        console.log(`URL prefix match: ${tabUrlClean} starts with ${blockedUrlClean}`);
        return true;
      }

      // Check if full tab URL contains blocked URL as a substring
      if (tabUrl.includes(blockedUrl)) {
        console.log(`URL substring match: ${tabUrl} contains ${blockedUrl}`);
        return true;
      }
    } catch (e) {
      console.log('Error parsing URLs for comparison:', e);

      // Fallback to simple string comparison
      if (tabUrl.includes(blockedUrl)) {
        console.log(`Simple substring match: ${tabUrl} contains ${blockedUrl}`);
        return true;
      }
    }

    // No match
    return false;
  } catch (e) {
    console.error('Error in URL comparison:', e);
    return false;
  }
}

// Helper: match keyword against URL, title, or page content
function isContainKeyword(word, url, title = '', pageContent = '') {
  try {
    // Skip empty keywords
    if (!word || word.trim() === '') {
      console.log('Empty keyword, skipping check');
      return false;
    }

    // Skip empty URLs
    if (!url) {
      console.log('Empty URL, skipping check');
      return false;
    }

    // Normalize inputs
    const tabUrl = url.toLowerCase();
    const tabTitle = (title || '').toLowerCase();
    const content = (pageContent || '').toLowerCase();

    // Normalize keyword
    word = word.toLowerCase().replace(/\s+/g, ' ').trim();

    // Create variations of the keyword for different types of matching
    const wordForSearchEngines = word.replace(/ /g, "+");
    const encodedWord = encodeURIComponent(word);

    // Try to decode the URL for better matching
    let decodedUrl = tabUrl;
    try {
      decodedUrl = decodeURIComponent(tabUrl);
    } catch (e) {
      console.log('Error decoding URL:', e);
    }

    // Debug
    console.log(`Checking keyword "${word}" against URL: ${tabUrl}`);

    // Skip browser internal pages
    if (tabUrl.includes("chrome-extension://") ||
        tabUrl.includes("chrome://") ||
        tabUrl.includes("about:") ||
        tabUrl.includes("file://") ||
        tabUrl.includes("data:") ||
        tabUrl.includes("javascript:")) {
      console.log('Browser internal page, skipping keyword check');
      return false;
    }

    // Check for matches
    const matchInUrl = tabUrl.includes(word);
    const matchInEncodedUrl = tabUrl.includes(encodedWord);
    const matchInDecodedUrl = decodedUrl.includes(word);
    const matchInDecodedEncodedUrl = decodedUrl.includes(encodedWord);
    const matchInSearchQuery = tabUrl.includes(wordForSearchEngines);
    const matchInTitle = tabTitle.includes(word);
    const matchInContent = content.includes(word);

    // Log matches for debugging
    if (matchInUrl) console.log(`Keyword "${word}" found directly in URL`);
    if (matchInEncodedUrl) console.log(`Keyword "${word}" (encoded) found in URL`);
    if (matchInDecodedUrl) console.log(`Keyword "${word}" found in decoded URL`);
    if (matchInDecodedEncodedUrl) console.log(`Keyword "${word}" (encoded) found in decoded URL`);
    if (matchInSearchQuery) console.log(`Keyword "${word}" found in search query format`);
    if (matchInTitle) console.log(`Keyword "${word}" found in page title`);
    if (matchInContent) console.log(`Keyword "${word}" found in page content`);

    // Return true if any match is found
    return matchInUrl ||
           matchInEncodedUrl ||
           matchInDecodedUrl ||
           matchInDecodedEncodedUrl ||
           matchInSearchQuery ||
           matchInTitle ||
           matchInContent;
  } catch (e) {
    console.error('Error in keyword matching:', e);
    return false;
  }
}

// Helper: check if now is within schedule
function isWithinSchedule(schedule) {
  if (!schedule) return true
  const now = new Date()
  const [startH, startM] = schedule.start.split(':').map(Number)
  const [endH, endM] = schedule.end.split(':').map(Number)
  const start = new Date(now)
  start.setHours(startH, startM, 0, 0)
  const end = new Date(now)
  end.setHours(endH, endM, 0, 0)
  if (end < start) end.setDate(end.getDate() + 1) // overnight
  return now >= start && now <= end
}

// Helper: get domain from URL
function getDomain(urlStr) {
  try {
    return new URL(urlStr).hostname.replace(/^www\./, '')
  } catch {
    return ''
  }
}

// Helper: check if two domains are related using generic algorithmic approach
function isRelatedDomain(testDomain, whitelistedDomain) {
  // Extract the main domain identifier from both domains using multiple strategies
  const extractDomainIdentifiers = (domain) => {
    const parts = domain.split('.');
    if (parts.length < 2) return [domain];

    const identifiers = [];
    const secondLastPart = parts[parts.length - 2];

    // Strategy 1: Use the main domain part as-is
    identifiers.push(secondLastPart);

    // Strategy 2: Extract from hyphenated domains (e.g., "amazon-adsystem" -> "amazon")
    if (secondLastPart.includes('-')) {
      const hyphenParts = secondLastPart.split('-');
      // Add each meaningful part (length >= 3)
      hyphenParts.forEach(part => {
        if (part.length >= 3) {
          identifiers.push(part);
        }
      });
    }

    // Strategy 3: Extract from camelCase or compound words (e.g., "googleapis" -> "google")
    // Look for common patterns where a shorter word might be embedded
    if (secondLastPart.length > 6) {
      // Try to find embedded shorter domains (3-8 chars) at the beginning
      for (let len = 3; len <= Math.min(8, secondLastPart.length - 2); len++) {
        const prefix = secondLastPart.substring(0, len);
        identifiers.push(prefix);
      }
    }

    // Strategy 4: Handle numeric suffixes (e.g., "example2" -> "example")
    const withoutNumbers = secondLastPart.replace(/\d+$/, '');
    if (withoutNumbers !== secondLastPart && withoutNumbers.length >= 3) {
      identifiers.push(withoutNumbers);
    }

    // Remove duplicates and filter meaningful identifiers
    return [...new Set(identifiers)].filter(id => id.length >= 3);
  };

  const testIdentifiers = extractDomainIdentifiers(testDomain);
  const whitelistedIdentifiers = extractDomainIdentifiers(whitelistedDomain);

  // Check for any matching identifiers
  for (const testId of testIdentifiers) {
    for (const whitelistedId of whitelistedIdentifiers) {
      if (testId === whitelistedId && testId.length >= 3) {
        console.log(`[IPC Handler] RELATED DOMAIN MATCH: '${testDomain}' (identifier: '${testId}') matches '${whitelistedDomain}' (identifier: '${whitelistedId}')`);
        return true;
      }
    }
  }

  // Additional check: if one domain contains the main part of another
  // This handles cases like "example.com" and "cdn.example-services.net"
  const testMainPart = testIdentifiers[0]; // Primary identifier
  const whitelistedMainPart = whitelistedIdentifiers[0]; // Primary identifier

  if (testMainPart && whitelistedMainPart && testMainPart.length >= 4 && whitelistedMainPart.length >= 4) {
    // Check if one is contained in the other (for compound domains)
    if (testMainPart.includes(whitelistedMainPart) || whitelistedMainPart.includes(testMainPart)) {
      console.log(`[IPC Handler] RELATED DOMAIN MATCH (CONTAINS): '${testDomain}' (main: '${testMainPart}') related to '${whitelistedDomain}' (main: '${whitelistedMainPart}')`);
      return true;
    }
  }

  return false;
}

// Helper function to check if a domain is whitelisted (for main process)
function isDomainWhitelistedInMain(domain, whitelistArray) {
  // Validate inputs
  if (!domain) {
    console.log('[IPC Handler] WHITELIST CHECK: Empty domain to test');
    return false;
  }

  if (!whitelistArray || !Array.isArray(whitelistArray) || whitelistArray.length === 0) {
    console.log('[IPC Handler] WHITELIST CHECK: Empty or invalid whitelist array');
    return false;
  }

  // Convert to string and normalize the domain we are testing
  const originalDomainToTest = String(domain);
  const normalizedDomainToTest = originalDomainToTest.toLowerCase().trim();

  console.log(`[IPC Handler] WHITELIST CHECK: Testing if '${originalDomainToTest}' (normalized: '${normalizedDomainToTest}') is in whitelist`);
  console.log('[IPC Handler] WHITELIST CHECK: Whitelist array:', whitelistArray);

  // Check each whitelist item
  for (const rawWhitelistedItem of whitelistArray) {
    if (!rawWhitelistedItem) {
      console.log('[IPC Handler] WHITELIST CHECK: Skipping empty whitelist item');
      continue;
    }

    // Normalize the item from the whitelist array
    const originalWhitelistedItem = String(rawWhitelistedItem);
    const normalizedWhitelistedItem = originalWhitelistedItem.toLowerCase().trim();

    console.log(`[IPC Handler] WHITELIST CHECK: Comparing with whitelist item '${originalWhitelistedItem}' (normalized: '${normalizedWhitelistedItem}')`);

    // Check for exact match
    if (normalizedDomainToTest === normalizedWhitelistedItem) {
      console.log(`[IPC Handler] WHITELIST MATCH (EXACT): '${normalizedDomainToTest}' exactly matches '${normalizedWhitelistedItem}'`);
      return true;
    }

    // IMPROVED: Check for related domain patterns (e.g., amazon.com should match s.amazon-adsystem.com)
    if (isRelatedDomain(normalizedDomainToTest, normalizedWhitelistedItem)) {
      console.log(`[IPC Handler] WHITELIST MATCH (RELATED): '${normalizedDomainToTest}' is related to '${normalizedWhitelistedItem}'`);
      return true;
    }

    // Check for subdomain match (e.g., mail.google.com matches google.com)
    if (normalizedDomainToTest.endsWith('.' + normalizedWhitelistedItem)) {
      console.log(`[IPC Handler] WHITELIST MATCH (SUBDOMAIN): '${normalizedDomainToTest}' is a subdomain of '${normalizedWhitelistedItem}'`);
      return true;
    }

    // Check for www variant match (e.g., www.example.com matches example.com)
    if (normalizedDomainToTest === 'www.' + normalizedWhitelistedItem ||
        normalizedWhitelistedItem === 'www.' + normalizedDomainToTest) {
      console.log(`[IPC Handler] WHITELIST MATCH (WWW VARIANT): '${normalizedDomainToTest}' is a www variant of '${normalizedWhitelistedItem}'`);
      return true;
    }

    // Check for TLD variants (.com, .org, etc.) by comparing domain without TLD
    try {
      // Extract domain without TLD for both domains
      const domainParts = normalizedDomainToTest.split('.');
      const whitelistParts = normalizedWhitelistedItem.split('.');

      // If both have at least 2 parts (domain and TLD)
      if (domainParts.length >= 2 && whitelistParts.length >= 2) {
        // Compare the domain part (without TLD)
        const domainWithoutTLD = domainParts.slice(0, -1).join('.');
        const whitelistWithoutTLD = whitelistParts.slice(0, -1).join('.');

        if (domainWithoutTLD === whitelistWithoutTLD) {
          console.log(`[IPC Handler] WHITELIST MATCH (TLD VARIANT): '${normalizedDomainToTest}' matches '${normalizedWhitelistedItem}' ignoring TLD`);
          return true;
        }
      }
    } catch (e) {
      console.error('[IPC Handler] Error comparing domain TLDs:', e);
    }

    // Special handling for short domains like nu.nl
    if (normalizedDomainToTest.length <= 5 && normalizedWhitelistedItem.length <= 5) {
      // For very short domains, be more lenient with matching
      if (normalizedDomainToTest.includes(normalizedWhitelistedItem) ||
          normalizedWhitelistedItem.includes(normalizedDomainToTest)) {
        console.log(`[IPC Handler] WHITELIST MATCH (SHORT DOMAIN): Short domain '${normalizedDomainToTest}' matches '${normalizedWhitelistedItem}'`);
        return true;
      }
    }

    // Additional check: if the domain to test contains the whitelisted item (for partial matches)
    // This is a more lenient check that might help with some edge cases
    if (normalizedDomainToTest.includes(normalizedWhitelistedItem) ||
        normalizedWhitelistedItem.includes(normalizedDomainToTest)) {
      console.log(`[IPC Handler] WHITELIST MATCH (CONTAINS): '${normalizedDomainToTest}' contains or is contained in '${normalizedWhitelistedItem}'`);
      console.log(`[IPC Handler] WARNING: This is a partial match and might cause unexpected behavior`);
      return true;
    }
  }

  // No special domain handling - all domains must be explicitly whitelisted

  // No match found
  console.log(`[IPC Handler] WHITELIST NO MATCH: '${normalizedDomainToTest}' (Original: ${originalDomainToTest}) not in whitelist`);
  console.log('[IPC Handler] WHITELIST NO MATCH: Normalized whitelist items:', whitelistArray.map(item => String(item).toLowerCase().trim()));
  return false;
}

// Track usage: { domain: [timestamps] }
let usage = {}
let override = { domain: '', until: 0 }

async function initStoreAndListeners() {
  const Store = (await import('electron-store')).default
  store = new Store({
    defaults: {
      settings: {
        blockList: '',
        timeLimits: '',
        schedules: '',
        password: '',
        redirectUrl: ''
      },
      usage: {},
      override: { domain: '', until: 0 }
    }
  })
  usage = store.get('usage') || {}
  override = store.get('override') || { domain: '', until: 0 }

  // IPC handler for preload script to check if images should be blocked
  ipcMain.handle('electronBrowser:should-block-images-for-url', async (event, url) => {
    try {
      if (!store) {
        console.error('[IPC Handler] Store not initialized. Cannot check image blocking rules.');
        return false; // Default to not blocking if store is not ready
      }

      // Get settings from store
      const settings = store.get('settings') || {};

      // Ensure 'blockImages' is treated as a string 'true' or 'false' as seen in other parts of the codebase
      const blockImagesGlobally = settings.blockImages === 'true';

      // If global image blocking is disabled, don't block any images
      if (!blockImagesGlobally) {
        console.log(`[IPC Handler] Images not blocked globally for ${url}`);
        return false; // Don't block if global setting is off
      }

      // Ensure imageWhitelist is parsed correctly, defaulting to an empty array
      let imageWhitelist = [];
      if (settings.imageWhitelist) {
        if (typeof settings.imageWhitelist === 'string') {
          try {
            imageWhitelist = JSON.parse(settings.imageWhitelist);
            if (!Array.isArray(imageWhitelist)) {
              console.warn('[IPC Handler] imageWhitelist was not an array, defaulting to empty.');
              imageWhitelist = [];
            }
          } catch (e) {
            console.error('[IPC Handler] Error parsing imageWhitelist JSON from settings:', e);
            imageWhitelist = []; // Default to empty on parse error
          }
        } else if (Array.isArray(settings.imageWhitelist)) {
          // If it's already an array
          imageWhitelist = settings.imageWhitelist;
        }
      }

      const currentDomain = getDomain(url); // Use existing getDomain helper
      if (!currentDomain) {
        console.log(`[IPC Handler] Could not extract domain from ${url}, not blocking.`);
        return false; // Don't block if domain extraction fails
      }

      // Check if the domain is whitelisted using improved logic
      const isWhitelisted = isDomainWhitelistedInMain(currentDomain, imageWhitelist);

      console.log(`[IPC Handler] For URL: ${url} (Domain: ${currentDomain}), GlobalBlock: ${blockImagesGlobally}, Whitelisted: ${isWhitelisted}, ShouldBlock: ${!isWhitelisted}`);

      return !isWhitelisted; // Block if global blocking is on AND domain is NOT whitelisted
    } catch (error) {
      console.error('[IPC Handler] Error in electronBrowser:should-block-images-for-url:', error);
      return false; // Default to not blocking on any error
    }
  });

  // Block images at network level, but respect the whitelist
  session.defaultSession.webRequest.onBeforeRequest(
    { urls: ['*://*/*.png', '*://*/*.jpg', '*://*/*.jpeg', '*://*/*.gif', '*://*/*.webp', '*://*/*.svg'] },
    (details, callback) => {
      try {
        // Skip if store is not initialized
        if (!store) {
          console.log('[Image Blocker] Store not initialized, allowing image:', details.url);
          callback({ cancel: false });
          return;
        }

        // Get settings from store
        const settings = store.get('settings') || {};

        // Check if image blocking is enabled
        const blockImagesGlobally = settings.blockImages === 'true';
        if (!blockImagesGlobally) {
          // Image blocking is disabled globally
          callback({ cancel: false });
          return;
        }

        // Get the whitelist
        let imageWhitelist = [];
        if (settings.imageWhitelist) {
          if (typeof settings.imageWhitelist === 'string') {
            try {
              imageWhitelist = JSON.parse(settings.imageWhitelist);
            } catch (e) {
              console.error('[Image Blocker] Error parsing imageWhitelist JSON:', e);
            }
          } else if (Array.isArray(settings.imageWhitelist)) {
            imageWhitelist = settings.imageWhitelist;
          }
        }

        // Extract domain from the image URL
        const url = details.url;
        const domain = getDomain(url);
        if (!domain) {
          // Can't determine domain, default to allowing the image
          console.log('[Image Blocker] Could not extract domain from URL, allowing image:', url);
          callback({ cancel: false });
          return;
        }

        // Check if the domain is whitelisted using improved logic
        const isWhitelisted = isDomainWhitelistedInMain(domain, imageWhitelist);

        // Block if not whitelisted
        console.log(`[Image Blocker] URL: ${url}, Domain: ${domain}, Whitelisted: ${isWhitelisted}, Blocking: ${!isWhitelisted}`);
        callback({ cancel: !isWhitelisted });
      } catch (error) {
        console.error('[Image Blocker] Error in image blocking rule:', error);
        // Default to allowing the image on error
        callback({ cancel: false });
      }
    }
  );
  // This is a global variable to track if we're in a redirect loop
  let isRedirecting = false;

  // Set up a filter for all HTTP and HTTPS requests
  session.defaultSession.webRequest.onBeforeRequest({ urls: ['http://*/*', 'https://*/*'] }, (details, callback) => {
    try {
      // Only block main frame navigations
      if (details.resourceType !== 'mainFrame') {
        callback({ cancel: false });
        return;
      }

      // Get the URL and log it
      const url = details.url;
      console.log('⚡ CHECKING URL:', url);

      // Skip if we're already redirecting to avoid loops
      if (isRedirecting) {
        console.log('⚠️ Already redirecting, allowing request to prevent loop');
        callback({ cancel: false });
        return;
      }

      // Skip block.html itself
      if (url.includes('block.html')) {
        console.log('⚠️ Block page requested, allowing');
        callback({ cancel: false });
        return;
      }

      // Get domain from URL
      let domain = '';
      try {
        const urlObj = new URL(url);
        domain = urlObj.hostname.toLowerCase();
        console.log('🌐 Domain:', domain);
      } catch (e) {
        console.error('❌ Error extracting domain:', e);
      }

      // Get settings from store - try both the settings object and individual keys
      const settings = store.get('settings') || {};

      // Get block lists from settings object
      let blockDomainsStr = settings.blockDomains || '';
      let blockUrlsStr = settings.blockUrls || '';
      let blockKeywordsStr = settings.blockKeywords || '';

      // If empty, try individual keys as fallback
      if (!blockDomainsStr) {
        blockDomainsStr = store.get('blockDomains') || '';
      }
      if (!blockUrlsStr) {
        blockUrlsStr = store.get('blockUrls') || '';
      }
      if (!blockKeywordsStr) {
        blockKeywordsStr = store.get('blockKeywords') || '';
      }

      // Don't try to access localStorage here - it's causing errors
      // We'll rely on the store values instead

      console.log('📋 Block domains string:', blockDomainsStr);
      console.log('📋 Block URLs string:', blockUrlsStr);
      console.log('📋 Block keywords string:', blockKeywordsStr);

      // Parse block lists
      const blockDomains = blockDomainsStr.split('\n').map(s => s.trim()).filter(Boolean);
      const blockUrls = blockUrlsStr.split('\n').map(s => s.trim()).filter(Boolean);
      const blockKeywords = blockKeywordsStr.split('\n').map(s => s.trim()).filter(Boolean);

      console.log('📋 Block domains array:', blockDomains);
      console.log('📋 Block URLs array:', blockUrls);
      console.log('📋 Block keywords array:', blockKeywords);

      // Check if domain is blocked
      if (domain && blockDomains.length > 0) {
        const normalizedDomain = domain.replace(/^www\./, '');

        for (const blockedDomain of blockDomains) {
          if (!blockedDomain) continue;

          const normalizedBlockedDomain = blockedDomain.toLowerCase().replace(/^www\./, '');

          // Check for exact match
          if (normalizedDomain === normalizedBlockedDomain) {
            console.log(`🚫 BLOCKED! Domain exact match: ${normalizedDomain} === ${normalizedBlockedDomain}`);
            blockAndRedirect(callback);
            return;
          }

          // Check for subdomain match
          if (normalizedDomain.endsWith('.' + normalizedBlockedDomain)) {
            console.log(`🚫 BLOCKED! Subdomain match: ${normalizedDomain} ends with ${normalizedBlockedDomain}`);
            blockAndRedirect(callback);
            return;
          }
        }
      }

      // Check if URL is blocked
      if (blockUrls.length > 0) {
        const lowerUrl = url.toLowerCase();

        for (const blockedUrl of blockUrls) {
          if (!blockedUrl) continue;

          const lowerBlockedUrl = blockedUrl.toLowerCase();

          if (lowerUrl.includes(lowerBlockedUrl)) {
            console.log(`🚫 BLOCKED! URL match: ${lowerUrl} contains ${lowerBlockedUrl}`);
            blockAndRedirect(callback);
            return;
          }
        }
      }

      // Check if URL contains blocked keyword
      if (blockKeywords.length > 0) {
        const lowerUrl = url.toLowerCase();

        for (const keyword of blockKeywords) {
          if (!keyword) continue;

          const lowerKeyword = keyword.toLowerCase();

          if (lowerUrl.includes(lowerKeyword)) {
            console.log(`🚫 BLOCKED! Keyword match: ${lowerUrl} contains ${lowerKeyword}`);
            blockAndRedirect(callback);
            return;
          }
        }
      }

      // Not blocked
      console.log('✅ URL allowed');
      callback({ cancel: false });
    } catch (error) {
      console.error('❌ Error in blocking logic:', error);
      callback({ cancel: false });
    }
  });

  // Function to block and redirect
  function blockAndRedirect(callback) {
    try {
      // Set redirecting flag to prevent loops
      isRedirecting = true;

      // Get redirect URL from settings
      const settings = store.get('settings') || {};
      const redirectUrl = settings.redirectUrl || '';

      if (redirectUrl && redirectUrl.startsWith('http')) {
        // Use custom redirect URL
        console.log(`➡️ Redirecting to custom URL: ${redirectUrl}`);
        callback({ cancel: false, redirectURL: redirectUrl });
      } else {
        // Use block.html page with absolute path
        const blockPagePath = path.join(__dirname, 'block.html');
        const blockPageUrl = url.format({
          pathname: blockPagePath,
          protocol: 'file:',
          slashes: true
        });
        console.log(`➡️ Redirecting to block page: ${blockPageUrl}`);
        callback({ cancel: false, redirectURL: blockPageUrl });
      }

      // Reset redirecting flag after a short delay
      setTimeout(() => {
        isRedirecting = false;
      }, 1000);
    } catch (error) {
      console.error('❌ Error in blockAndRedirect:', error);
      isRedirecting = false;
      callback({ cancel: false });
    }
  }

  // Special handler for block.html requests
  session.defaultSession.webRequest.onBeforeRequest(
    { urls: ['*://*/*block.html*', 'file://*/block.html*'] },
    (details, callback) => {
      console.log('🔍 Block page requested:', details.url);
      // Always allow block.html to load
      callback({ cancel: false });
    }
  );

  // Reset the redirecting flag periodically to prevent getting stuck
  setInterval(() => {
    isRedirecting = false;
  }, 5000);





  // IPC: Save settings - COMPLETELY REWRITTEN
  ipcMain.on('save-settings', (event, settings) => {
    console.log('🔧 SAVE SETTINGS - Received settings from renderer:', settings);

    try {
      // Ensure settings is an object
      if (!settings || typeof settings !== 'object') {
        console.error('❌ Invalid settings received:', settings);
        settings = {};
      }

      // Log the block lists for debugging
      console.log('🔧 SAVE SETTINGS - Raw block domains:', settings.blockDomains);
      console.log('🔧 SAVE SETTINGS - Raw block URLs:', settings.blockUrls);
      console.log('🔧 SAVE SETTINGS - Raw block keywords:', settings.blockKeywords);

      // Parse block lists
      const blockDomains = settings.blockDomains ? settings.blockDomains.split('\n').map(s => s.trim()).filter(Boolean) : [];
      const blockUrls = settings.blockUrls ? settings.blockUrls.split('\n').map(s => s.trim()).filter(Boolean) : [];
      const blockKeywords = settings.blockKeywords ? settings.blockKeywords.split('\n').map(s => s.trim()).filter(Boolean) : [];

      console.log('🔧 SAVE SETTINGS - Parsed block domains:', blockDomains);
      console.log('🔧 SAVE SETTINGS - Parsed block URLs:', blockUrls);
      console.log('🔧 SAVE SETTINGS - Parsed block keywords:', blockKeywords);

      // Save settings to store
      store.set('settings', settings);
      console.log('✅ SAVE SETTINGS - Settings saved to store');

      // Verify settings were saved correctly
      const savedSettings = store.get('settings');
      console.log('✅ SAVE SETTINGS - Verified saved settings:', savedSettings);

      // Also save to individual keys for redundancy
      if (settings.blockDomains) {
        store.set('blockDomains', settings.blockDomains);
      }
      if (settings.blockUrls) {
        store.set('blockUrls', settings.blockUrls);
      }
      if (settings.blockKeywords) {
        store.set('blockKeywords', settings.blockKeywords);
      }
      if (settings.redirectUrl) {
        store.set('redirectUrl', settings.redirectUrl);
      }

      // Verify individual keys
      console.log('✅ SAVE SETTINGS - Verified blockDomains:', store.get('blockDomains'));
      console.log('✅ SAVE SETTINGS - Verified blockUrls:', store.get('blockUrls'));
      console.log('✅ SAVE SETTINGS - Verified blockKeywords:', store.get('blockKeywords'));
      console.log('✅ SAVE SETTINGS - Verified redirectUrl:', store.get('redirectUrl'));

      // Reply to renderer
      event.reply('settings-saved');

      // Force reload the webContents to apply settings
      console.log('🔄 SAVE SETTINGS - Reloading main window to apply settings');
      setTimeout(() => {
        try {
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.reload();
          }
        } catch (e) {
          console.error('❌ Error reloading main window:', e);
        }
      }, 500);
    } catch (error) {
      console.error('❌ Error in save-settings handler:', error);
      // Reply to renderer even if there was an error
      event.reply('settings-saved');
    }
  })

  // IPC: Open settings
  ipcMain.on('open-settings', (event, tab) => {
    console.log('Opening settings tab:', tab);
    mainWindow.webContents.executeJavaScript(`
      document.getElementById('settings-btn').click();
      if ('${tab}' === 'advanced') {
        setTimeout(() => {
          document.getElementById('advanced-tab').click();
        }, 500);
      }
    `).catch(err => console.error('Error opening settings:', err));
  })
  // IPC: Load settings
  ipcMain.handle('load-settings', () => store.get('settings'))
  // IPC: Override
  ipcMain.on('override', (event, { domain, minutes }) => {
    override = { domain, until: Date.now() + minutes * 60 * 1000 }
    store.set('override', override)
    event.reply('override-set')
  })
  // IPC: Get stats
  ipcMain.handle('get-stats', () => ({ usage, override }))

  // IPC: Update image whitelist
  ipcMain.on('update-whitelist', (event, whitelist) => {
    try {
      console.log('[IPC] Received image whitelist update from renderer:', whitelist);

      // Validate the whitelist
      if (!whitelist || !Array.isArray(whitelist)) {
        console.error('[IPC] Invalid image whitelist format received:', whitelist);
        whitelist = [];
      }

      // Convert any non-string items to strings
      whitelist = whitelist.map(item => String(item));

      // Update the whitelist in settings
      const settings = store.get('settings') || {};

      // Update the whitelist in settings
      if (typeof settings.imageWhitelist === 'string') {
        settings.imageWhitelist = JSON.stringify(whitelist);
      } else {
        settings.imageWhitelist = whitelist;
      }

      // Save the updated settings
      store.set('settings', settings);

      console.log('[IPC] Image whitelist updated in settings:', settings.imageWhitelist);

      // Verify the whitelist was saved correctly
      const savedSettings = store.get('settings');
      console.log('[IPC] Verified image whitelist:', savedSettings.imageWhitelist);

      // Check if nu.nl is in the whitelist
      const hasNuNl = whitelist.some(item =>
        item.toLowerCase().includes('nu.nl') ||
        item.toLowerCase() === 'nu.nl');
      console.log(`[IPC] nu.nl is in image whitelist: ${hasNuNl}`);

      // Check if the whitelist contains any items
      console.log(`[IPC] Image whitelist contains ${whitelist.length} items`);
      whitelist.forEach((item, index) => {
        console.log(`[IPC] Image whitelist item ${index}: "${item}"`);
      });

      // No special domain handling - only domains explicitly added by the user are in the whitelist
      console.log('[IPC] Image whitelist contains only user-added domains:', whitelist);

      // Send confirmation back to renderer
      event.reply('whitelist-updated', { success: true });
    } catch (error) {
      console.error('[IPC] Error updating image whitelist:', error);
      event.reply('whitelist-updated', { success: false, error: error.message });
    }
  });

  // IPC: Update video whitelist
  ipcMain.on('update-video-whitelist', (event, whitelist) => {
    try {
      console.log('[IPC] Received video whitelist update from renderer:', whitelist);

      // Validate the whitelist
      if (!whitelist || !Array.isArray(whitelist)) {
        console.error('[IPC] Invalid video whitelist format received:', whitelist);
        whitelist = [];
      }

      // Convert any non-string items to strings
      whitelist = whitelist.map(item => String(item));

      // Update the whitelist in settings
      const settings = store.get('settings') || {};

      // Update the whitelist in settings
      if (typeof settings.videoWhitelist === 'string') {
        settings.videoWhitelist = JSON.stringify(whitelist);
      } else {
        settings.videoWhitelist = whitelist;
      }

      // Save the updated settings
      store.set('settings', settings);

      console.log('[IPC] Video whitelist updated in settings:', settings.videoWhitelist);

      // Verify the whitelist was saved correctly
      const savedSettings = store.get('settings');
      console.log('[IPC] Verified video whitelist:', savedSettings.videoWhitelist);

      // Send confirmation back to renderer
      event.reply('video-whitelist-updated', { success: true });
    } catch (error) {
      console.error('[IPC] Error updating video whitelist:', error);
      event.reply('video-whitelist-updated', { success: false, error: error.message });
    }
  });
}

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    title: "Electron Browser",
    frame: false,
    titleBarStyle: 'hidden',
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      webviewTag: true,
      enableRemoteModule: true,
      backgroundThrottling: false,
      webSecurity: true,
      allowRunningInsecureContent: false,
      partition: 'persist:main'
    },
  })

  // Enable remote module
  require('@electron/remote/main').initialize()
  require('@electron/remote/main').enable(mainWindow.webContents)

  // Load the app.html file
  mainWindow.loadFile('app.html')

  // Handle errors
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
    console.error('Failed to load:', errorCode, errorDescription)
    mainWindow.loadFile('app.html')
  })

  // Set up download handler for default session
  if (session && session.defaultSession) {
    setupDownloadHandler(session.defaultSession);
  }

  // Handle webContents creation for webviews with enhanced error handling
  app.on('web-contents-created', (event, contents) => {
    if (contents.getType() === 'webview') {
      console.log('Webview created, setting up enhanced handlers');

      // Set up download handler for this webview's session
      setupDownloadHandler(contents.session);

      // Enhanced session configuration for better compatibility
      try {
        const session = contents.session;

        // Configure session for better compatibility with complex sites
        session.setPermissionRequestHandler((webContents, permission, callback) => {
          console.log('Permission requested:', permission);
          callback(true); // Allow all permissions for compatibility
        });

        // Set user agent for better compatibility
        session.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36');

        // Enhanced web request handling to prevent navigation conflicts
        session.webRequest.onBeforeRequest((details, callback) => {
          // Special handling for AliExpress URLs to prevent ERR_ABORTED
          if (details.url && details.url.includes('aliexpress.com') && details.resourceType === 'mainFrame') {
            console.log('AliExpress main frame request intercepted:', details.url);

            // Check if this is a complex URL that might cause ERR_ABORTED
            try {
              const urlObj = new URL(details.url);
              if (urlObj.search.includes('spm=') || urlObj.search.includes('gatewayAdapt') || urlObj.pathname.includes('/w/')) {
                console.log('Complex AliExpress URL detected, redirecting to base domain first');

                // Redirect to base domain instead of complex URL
                const baseDomain = `${urlObj.protocol}//${urlObj.hostname}/`;
                callback({ redirectURL: baseDomain });

                // Schedule navigation to original URL after base domain loads
                setTimeout(() => {
                  try {
                    // Send message to renderer to navigate to original URL
                    if (mainWindow && mainWindow.webContents) {
                      mainWindow.webContents.send('delayed-aliexpress-navigation', details.url);
                    }
                  } catch (delayedError) {
                    console.error('Error scheduling delayed AliExpress navigation:', delayedError);
                  }
                }, 5000);

                return;
              }
            } catch (urlError) {
              console.error('Error parsing AliExpress URL in webRequest:', urlError);
            }
          }

          // Log main frame requests for debugging
          if (details.resourceType === 'mainFrame') {
            console.log('Main frame request:', details.url);
          }

          callback({});
        });

        console.log('Enhanced session configuration applied');
      } catch (sessionError) {
        console.error('Error configuring webview session:', sessionError);
      }

      // Allow opening links in new windows/tabs
      contents.on('new-window', (event, url) => {
        event.preventDefault();
        mainWindow.webContents.send('create-new-tab', url);
      });

      // Enhanced navigation handling
      contents.on('will-navigate', (event, url) => {
        console.log('Webview will-navigate:', url);

        if (url.toLowerCase().endsWith('.pdf')) {
          console.log('PDF navigation detected, allowing manual download:', url);
        }

        // Don't prevent navigation - let complex sites handle their redirects
      });

      // Add context menu for links to allow "Save link as..."
      contents.on('context-menu', (event, params) => {
        if (params.linkURL) {
          const menu = require('electron').Menu.buildFromTemplate([
            {
              label: 'Open Link in New Tab',
              click: () => {
                mainWindow.webContents.send('create-new-tab', params.linkURL);
              }
            },
            {
              label: 'Save Link As...',
              click: () => {
                contents.downloadURL(params.linkURL);
              }
            },
            { type: 'separator' },
            {
              label: 'Copy Link Address',
              click: () => {
                require('electron').clipboard.writeText(params.linkURL);
              }
            }
          ]);
          menu.popup();
        }
      });

      // Handle webview crashes using modern event
      contents.on('render-process-gone', (event, details) => {
        console.log('Webview render process gone:', details.reason, 'exitCode:', details.exitCode);

        // Only reload for crashes, not for normal exits
        if (details.reason === 'crashed' || details.reason === 'oom-killed') {
          console.log('Webview crashed, but letting renderer handle recovery to avoid conflicts');

          // Don't reload here - let the renderer-side webview-handler.js handle recovery
          // This prevents conflicts between main process and renderer process recovery attempts

          // Just notify the main window about the crash
          try {
            if (mainWindow && !mainWindow.isDestroyed()) {
              mainWindow.webContents.send('webview-crashed', {
                reason: details.reason,
                exitCode: details.exitCode
              });
            }
          } catch (err) {
            console.error('Error notifying main window about webview crash:', err);
          }
        } else {
          console.log('Webview process ended normally, reason:', details.reason);
        }
      });

      // Handle webview responsiveness - let renderer handle recovery
      contents.on('unresponsive', () => {
        console.log('Webview unresponsive, letting renderer handle recovery');

        // Notify the main window about unresponsiveness instead of handling here
        try {
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('webview-unresponsive');
          }
        } catch (err) {
          console.error('Error notifying main window about webview unresponsiveness:', err);
        }
      });

      // Handle when webview becomes responsive again
      contents.on('responsive', () => {
        console.log('Webview became responsive again');

        // Notify the main window
        try {
          if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('webview-responsive');
          }
        } catch (err) {
          console.error('Error notifying main window about webview responsiveness:', err);
        }
      });

      // Handle navigation errors
      contents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL, isMainFrame) => {
        console.error(`Navigation failed: ${validatedURL}, Error: ${errorCode} (${errorDescription}), MainFrame: ${isMainFrame}`);

        // Handle specific error codes
        if (errorCode === -2) { // ERR_FAILED
          console.log('ERR_FAILED detected, this might be due to network issues or site blocking');

          // Don't retry immediately for ERR_FAILED as it often indicates a permanent failure
          if (isMainFrame) {
            console.log('Main frame failed to load, user can manually retry if needed');
          }
        } else if (errorCode === -3) { // ERR_ABORTED
          console.log('Navigation was aborted for:', validatedURL, 'MainFrame:', isMainFrame);

          // Enhanced ERR_ABORTED handling for complex sites like AliExpress
          if (isMainFrame && validatedURL) {
            console.log('ERR_ABORTED in main frame, checking if retry is appropriate');

            // Check if this looks like an AliExpress redirect failure
            if (validatedURL.includes('aliexpress.com') || validatedURL.includes('gatewayAdapt')) {
              console.log('AliExpress navigation detected, this may be a legitimate redirect failure');

              // Don't automatically retry here - let the webview-handler.js handle it
              // This prevents conflicts between main process and webview process handling
            }
          }
        } else if (errorCode === -105) { // ERR_NAME_NOT_RESOLVED
          console.log('DNS resolution failed for:', validatedURL);
        }
      });
    }
  });

  // Set up URL blocking handlers

  // This is a global variable to track if we're in a redirect loop
  let isRedirecting = false;

  // Reset the redirecting flag periodically to prevent getting stuck
  setInterval(() => {
    isRedirecting = false;
  }, 5000);

  // Handle will-navigate events
  mainWindow.webContents.on('will-navigate', (event, url) => {
    try {
      console.log('🔍 will-navigate event for URL:', url);

      // Skip internal URLs and block page itself
      if (url.includes('block.html') ||
          url.startsWith('chrome://') ||
          url.startsWith('chrome-extension://') ||
          url.startsWith('devtools://') ||
          url.startsWith('file://') ||
          url.startsWith('data:') ||
          url.startsWith('about:') ||
          url.startsWith('blob:')) {
        console.log('✅ Internal URL, allowing navigation');
        return;
      }

      // Get domain from URL
      let domain = '';
      try {
        const urlObj = new URL(url);
        domain = urlObj.hostname.toLowerCase();
        console.log('🌐 Domain:', domain);
      } catch (e) {
        console.error('❌ Error extracting domain:', e);
      }

      // Get settings from store
      const settings = store.get('settings') || {};

      // Get block lists
      let blockDomainsStr = settings.blockDomains || '';
      let blockUrlsStr = settings.blockUrls || '';
      let blockKeywordsStr = settings.blockKeywords || '';

      // If empty, try individual keys as fallback
      if (!blockDomainsStr) {
        blockDomainsStr = store.get('blockDomains') || '';
      }
      if (!blockUrlsStr) {
        blockUrlsStr = store.get('blockUrls') || '';
      }
      if (!blockKeywordsStr) {
        blockKeywordsStr = store.get('blockKeywords') || '';
      }

      console.log('📋 Block domains string:', blockDomainsStr);
      console.log('📋 Block URLs string:', blockUrlsStr);
      console.log('📋 Block keywords string:', blockKeywordsStr);

      // Parse block lists
      const blockDomains = blockDomainsStr.split('\n').map(s => s.trim()).filter(Boolean);
      const blockUrls = blockUrlsStr.split('\n').map(s => s.trim()).filter(Boolean);
      const blockKeywords = blockKeywordsStr.split('\n').map(s => s.trim()).filter(Boolean);

      // Check if domain is blocked
      if (domain && blockDomains.length > 0) {
        const normalizedDomain = domain.replace(/^www\./, '');

        for (const blockedDomain of blockDomains) {
          if (!blockedDomain) continue;

          const normalizedBlockedDomain = blockedDomain.toLowerCase().replace(/^www\./, '');

          // Check for exact match
          if (normalizedDomain === normalizedBlockedDomain) {
            console.log(`🚫 BLOCKED! Domain exact match: ${normalizedDomain} === ${normalizedBlockedDomain}`);
            event.preventDefault();
            mainWindow.loadFile('block.html');
            return;
          }

          // Check for subdomain match
          if (normalizedDomain.endsWith('.' + normalizedBlockedDomain)) {
            console.log(`🚫 BLOCKED! Subdomain match: ${normalizedDomain} ends with ${normalizedBlockedDomain}`);
            event.preventDefault();
            mainWindow.loadFile('block.html');
            return;
          }
        }
      }

      // Check if URL is blocked
      if (blockUrls.length > 0) {
        const lowerUrl = url.toLowerCase();

        for (const blockedUrl of blockUrls) {
          if (!blockedUrl) continue;

          const lowerBlockedUrl = blockedUrl.toLowerCase();

          if (lowerUrl.includes(lowerBlockedUrl)) {
            console.log(`🚫 BLOCKED! URL match: ${lowerUrl} contains ${lowerBlockedUrl}`);
            event.preventDefault();
            mainWindow.loadFile('block.html');
            return;
          }
        }
      }

      // Check if URL contains blocked keyword
      if (blockKeywords.length > 0) {
        const lowerUrl = url.toLowerCase();

        for (const keyword of blockKeywords) {
          if (!keyword) continue;

          const lowerKeyword = keyword.toLowerCase();

          if (lowerUrl.includes(lowerKeyword)) {
            console.log(`🚫 BLOCKED! Keyword match: ${lowerUrl} contains ${lowerKeyword}`);
            event.preventDefault();
            mainWindow.loadFile('block.html');
            return;
          }
        }
      }

      console.log('✅ URL allowed');
    } catch (error) {
      console.error('❌ Error in will-navigate handler:', error);
    }
  });

  // Handle did-start-navigation events
  mainWindow.webContents.on('did-start-navigation', (event, url, isInPlace, isMainFrame) => {
    try {
      // Only block main frame navigations
      if (!isMainFrame) {
        return;
      }

      console.log('🔍 did-start-navigation event for URL:', url);

      // Skip internal URLs and block page itself
      if (url.includes('block.html') ||
          url.startsWith('chrome://') ||
          url.startsWith('chrome-extension://') ||
          url.startsWith('devtools://') ||
          url.startsWith('file://') ||
          url.startsWith('data:') ||
          url.startsWith('about:') ||
          url.startsWith('blob:')) {
        console.log('✅ Internal URL, allowing navigation');
        return;
      }

      // Get domain from URL
      let domain = '';
      try {
        const urlObj = new URL(url);
        domain = urlObj.hostname.toLowerCase();
        console.log('🌐 Domain:', domain);
      } catch (e) {
        console.error('❌ Error extracting domain:', e);
      }

      // Get settings from store
      const settings = store.get('settings') || {};

      // Get block lists
      let blockDomainsStr = settings.blockDomains || '';
      let blockUrlsStr = settings.blockUrls || '';
      let blockKeywordsStr = settings.blockKeywords || '';

      // If empty, try individual keys as fallback
      if (!blockDomainsStr) {
        blockDomainsStr = store.get('blockDomains') || '';
      }
      if (!blockUrlsStr) {
        blockUrlsStr = store.get('blockUrls') || '';
      }
      if (!blockKeywordsStr) {
        blockKeywordsStr = store.get('blockKeywords') || '';
      }

      // Parse block lists
      const blockDomains = blockDomainsStr.split('\n').map(s => s.trim()).filter(Boolean);
      const blockUrls = blockUrlsStr.split('\n').map(s => s.trim()).filter(Boolean);
      const blockKeywords = blockKeywordsStr.split('\n').map(s => s.trim()).filter(Boolean);

      // Check if domain is blocked
      if (domain && blockDomains.length > 0) {
        const normalizedDomain = domain.replace(/^www\./, '');

        for (const blockedDomain of blockDomains) {
          if (!blockedDomain) continue;

          const normalizedBlockedDomain = blockedDomain.toLowerCase().replace(/^www\./, '');

          // Check for exact match
          if (normalizedDomain === normalizedBlockedDomain) {
            console.log(`🚫 BLOCKED! Domain exact match: ${normalizedDomain} === ${normalizedBlockedDomain}`);
            mainWindow.loadFile('block.html');
            return;
          }

          // Check for subdomain match
          if (normalizedDomain.endsWith('.' + normalizedBlockedDomain)) {
            console.log(`🚫 BLOCKED! Subdomain match: ${normalizedDomain} ends with ${normalizedBlockedDomain}`);
            mainWindow.loadFile('block.html');
            return;
          }
        }
      }

      // Check if URL is blocked
      if (blockUrls.length > 0) {
        const lowerUrl = url.toLowerCase();

        for (const blockedUrl of blockUrls) {
          if (!blockedUrl) continue;

          const lowerBlockedUrl = blockedUrl.toLowerCase();

          if (lowerUrl.includes(lowerBlockedUrl)) {
            console.log(`🚫 BLOCKED! URL match: ${lowerUrl} contains ${lowerBlockedUrl}`);
            mainWindow.loadFile('block.html');
            return;
          }
        }
      }

      // Check if URL contains blocked keyword
      if (blockKeywords.length > 0) {
        const lowerUrl = url.toLowerCase();

        for (const keyword of blockKeywords) {
          if (!keyword) continue;

          const lowerKeyword = keyword.toLowerCase();

          if (lowerUrl.includes(lowerKeyword)) {
            console.log(`🚫 BLOCKED! Keyword match: ${lowerUrl} contains ${lowerKeyword}`);
            mainWindow.loadFile('block.html');
            return;
          }
        }
      }

      console.log('✅ URL allowed');
    } catch (error) {
      console.error('❌ Error in did-start-navigation handler:', error);
    }
  });

  // Handle new-window events
  mainWindow.webContents.on('new-window', (event, url) => {
    try {
      console.log('🔍 new-window event for URL:', url);

      // Skip internal URLs and block page itself
      if (url.includes('block.html') ||
          url.startsWith('chrome://') ||
          url.startsWith('chrome-extension://') ||
          url.startsWith('devtools://') ||
          url.startsWith('file://') ||
          url.startsWith('data:') ||
          url.startsWith('about:') ||
          url.startsWith('blob:')) {
        console.log('✅ Internal URL, allowing new window');
        return;
      }

      // Get domain from URL
      let domain = '';
      try {
        const urlObj = new URL(url);
        domain = urlObj.hostname.toLowerCase();
        console.log('🌐 Domain:', domain);
      } catch (e) {
        console.error('❌ Error extracting domain:', e);
      }

      // Get settings from store
      const settings = store.get('settings') || {};

      // Get block lists
      let blockDomainsStr = settings.blockDomains || '';
      let blockUrlsStr = settings.blockUrls || '';
      let blockKeywordsStr = settings.blockKeywords || '';

      // If empty, try individual keys as fallback
      if (!blockDomainsStr) {
        blockDomainsStr = store.get('blockDomains') || '';
      }
      if (!blockUrlsStr) {
        blockUrlsStr = store.get('blockUrls') || '';
      }
      if (!blockKeywordsStr) {
        blockKeywordsStr = store.get('blockKeywords') || '';
      }

      // Parse block lists
      const blockDomains = blockDomainsStr.split('\n').map(s => s.trim()).filter(Boolean);
      const blockUrls = blockUrlsStr.split('\n').map(s => s.trim()).filter(Boolean);
      const blockKeywords = blockKeywordsStr.split('\n').map(s => s.trim()).filter(Boolean);

      // Check if domain is blocked
      if (domain && blockDomains.length > 0) {
        const normalizedDomain = domain.replace(/^www\./, '');

        for (const blockedDomain of blockDomains) {
          if (!blockedDomain) continue;

          const normalizedBlockedDomain = blockedDomain.toLowerCase().replace(/^www\./, '');

          // Check for exact match
          if (normalizedDomain === normalizedBlockedDomain) {
            console.log(`🚫 BLOCKED! Domain exact match in new window: ${normalizedDomain} === ${normalizedBlockedDomain}`);
            event.preventDefault();
            mainWindow.loadFile('block.html');
            return;
          }

          // Check for subdomain match
          if (normalizedDomain.endsWith('.' + normalizedBlockedDomain)) {
            console.log(`🚫 BLOCKED! Subdomain match in new window: ${normalizedDomain} ends with ${normalizedBlockedDomain}`);
            event.preventDefault();
            mainWindow.loadFile('block.html');
            return;
          }
        }
      }

      // Check if URL is blocked
      if (blockUrls.length > 0) {
        const lowerUrl = url.toLowerCase();

        for (const blockedUrl of blockUrls) {
          if (!blockedUrl) continue;

          const lowerBlockedUrl = blockedUrl.toLowerCase();

          if (lowerUrl.includes(lowerBlockedUrl)) {
            console.log(`🚫 BLOCKED! URL match in new window: ${lowerUrl} contains ${lowerBlockedUrl}`);
            event.preventDefault();
            mainWindow.loadFile('block.html');
            return;
          }
        }
      }

      // Check if URL contains blocked keyword
      if (blockKeywords.length > 0) {
        const lowerUrl = url.toLowerCase();

        for (const keyword of blockKeywords) {
          if (!keyword) continue;

          const lowerKeyword = keyword.toLowerCase();

          if (lowerUrl.includes(lowerKeyword)) {
            console.log(`🚫 BLOCKED! Keyword match in new window: ${lowerUrl} contains ${lowerKeyword}`);
            event.preventDefault();
            mainWindow.loadFile('block.html');
            return;
          }
        }
      }

      console.log('✅ URL allowed in new window');
    } catch (error) {
      console.error('❌ Error in new-window handler:', error);
    }
  });

  // Handle did-finish-load events for keyword blocking in page content
  mainWindow.webContents.on('did-finish-load', () => {
    // Only inject on http/https pages
    const url = mainWindow.webContents.getURL();
    if (!/^https?:\/\//i.test(url)) return;

    mainWindow.webContents.executeJavaScript(`
      (function() {
        try {
          if (!document.body) return;
          const blockKeywords = (window.localStorage.getItem('blockKeywords') || '').split('\\n').map(x => x.trim()).filter(Boolean);
          if (!blockKeywords.length) return;
          const title = document.title || '';
          const bodyText = document.body ? document.body.innerText : '';
          for (const keyword of blockKeywords) {
            if (
              title.toLowerCase().includes(keyword.toLowerCase()) ||
              bodyText.toLowerCase().includes(keyword.toLowerCase())
            ) {
              document.body.innerHTML = '<div style="text-align:center;padding:60px;background:#f44336;color:#fff;font-size:2em;border-radius:16px;margin:60px auto;max-width:600px;box-shadow:0 8px 32px rgba(0,0,0,0.2);">⛔<br><b>Blocked by keyword:</b> ' + keyword + '<br><br>This page is blocked by your browser settings.<br><br><button onclick=\"window.location.href=\\'about:blank\\'\" style=\"margin-top:30px;padding:12px 32px;font-size:1em;background:#fff;color:#d32f2f;border:none;border-radius:8px;cursor:pointer;\">Go Back</button></div>';
              break;
            }
          }
        } catch (e) {
          // Fail silently
        }
      })();
    `).catch(e => {
      console.error('Injected block keyword script failed:', e);
    });
  });
}

// Flag to track if we're already handling quit
let isQuitting = false;

// Enhanced global error handlers for better crash recovery
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);

  // Handle specific GUEST_VIEW_MANAGER_CALL errors
  if (error.message && error.message.includes('GUEST_VIEW_MANAGER_CALL')) {
    console.log('GUEST_VIEW_MANAGER_CALL error detected, this is usually related to webview lifecycle issues');
    console.log('The application will continue running, but some webviews may need to be reloaded');

    // Suppress the error to prevent it from crashing the app
    return;
  }

  // Handle ERR_ABORTED errors that might cause GUEST_VIEW_MANAGER_CALL issues
  if (error.message && (error.message.includes('ERR_ABORTED') || error.code === 'ERR_ABORTED')) {
    console.log('ERR_ABORTED error detected in global handler, suppressing to prevent crashes');

    // Check if this is an AliExpress-related error
    if (error.message && error.message.includes('aliexpress.com')) {
      console.log('AliExpress-related ERR_ABORTED detected, this is expected due to complex redirects');
    }

    return;
  }

  // Handle navigation-related errors
  if (error.message && (error.message.includes('navigation') || error.message.includes('webContents'))) {
    console.log('Navigation-related error detected, suppressing to maintain stability');
    return;
  }
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);

  // Handle promise rejections related to webview operations
  if (reason && reason.toString().includes('ERR_FAILED')) {
    console.log('ERR_FAILED promise rejection detected, this is usually related to navigation failures');
  }

  // Handle GUEST_VIEW_MANAGER_CALL rejections
  if (reason && reason.toString().includes('GUEST_VIEW_MANAGER_CALL')) {
    console.log('GUEST_VIEW_MANAGER_CALL promise rejection detected, suppressing to prevent crashes');
    return;
  }

  // Handle ERR_ABORTED rejections
  if (reason && (reason.toString().includes('ERR_ABORTED') || (reason.code && reason.code === 'ERR_ABORTED'))) {
    console.log('ERR_ABORTED promise rejection detected, suppressing to prevent crashes');
    return;
  }
});

// This method will be called when Electron has finished initialization
app.whenReady().then(async () => {
  // Cache clearing on startup has been removed
  await initStoreAndListeners()

  // Restore saved cookies if any
  try {
    const savedCookies = store ? store.get('savedCookies') : null;
    if (savedCookies && Array.isArray(savedCookies) && savedCookies.length > 0) {
      console.log(`Restoring ${savedCookies.length} saved cookies from previous session`);
      const ses = session.defaultSession;

      let successCount = 0;
      let errorCount = 0;

      for (const cookie of savedCookies) {
        try {
          if (!cookie || !cookie.domain || !cookie.name) {
            console.warn('Skipping invalid cookie:', cookie);
            continue;
          }

          // Format cookie for setting
          const cookieDetails = {
            url: (cookie.secure ? 'https://' : 'http://') + (cookie.domain.startsWith('.') ? 'www' + cookie.domain : cookie.domain) + (cookie.path || '/'),
            name: cookie.name,
            value: cookie.value || '',
            domain: cookie.domain,
            path: cookie.path || '/',
            secure: !!cookie.secure,
            httpOnly: !!cookie.httpOnly,
            expirationDate: cookie.expirationDate || (Math.floor(Date.now() / 1000) + 31536000), // Default to 1 year if missing
            sameSite: cookie.sameSite || 'lax'
          };

          await ses.cookies.set(cookieDetails);
          successCount++;
        } catch (err) {
          console.error('Error restoring saved cookie:', err, 'Cookie:', cookie);
          errorCount++;
        }
      }

      console.log(`Cookie restoration complete: ${successCount} succeeded, ${errorCount} failed`);

      // Clear saved cookies after restoring
      try {
        store.delete('savedCookies');
      } catch (clearErr) {
        console.error('Error clearing saved cookies from store:', clearErr);
      }
    }
  } catch (e) {
    console.error('Error restoring saved cookies:', e);
  }

  createWindow()

  // Set up before-quit handler to clear cookies based on settings
  app.on('before-quit', async (event) => {
    // Prevent multiple executions of the quit handler
    if (isQuitting) {
      console.log('Already handling quit, skipping duplicate event');
      return;
    }

    // Set the flag to indicate we're handling quit
    isQuitting = true;
    console.log('Starting application quit process');

    try {
      // Get settings
      const settings = store ? store.get('settings') : {};
      const saveCookies = settings && settings.saveCookies === 'true';
      const cookieWhitelist = settings && settings.cookieWhitelist ? settings.cookieWhitelist : [];

      // If save cookies is enabled, don't clear anything
      if (saveCookies) {
        console.log('Save cookies enabled, not clearing cookies on exit');
        return;
      }

      // If we have a whitelist, we need to handle it
      if (cookieWhitelist && cookieWhitelist.length > 0) {
        console.log('Using cookie whitelist on exit:', cookieWhitelist);

        // Prevent immediate quit to allow async operations
        event.preventDefault();

        const ses = session.defaultSession;

        // Get all cookies
        const allCookies = await ses.cookies.get({});

        // Filter cookies that match the whitelist
        const whitelistedCookies = allCookies.filter(cookie => {
          const domain = cookie.domain.startsWith('.') ? cookie.domain.substring(1) : cookie.domain;

          // Check if this cookie's domain matches any whitelist entry
          return cookieWhitelist.some(whitelistDomain => {
            // Normalize domains for comparison
            const normalizedWhitelistDomain = whitelistDomain.toLowerCase().trim();
            const normalizedCookieDomain = domain.toLowerCase().trim();

            // Check for exact match or subdomain match
            return normalizedCookieDomain === normalizedWhitelistDomain ||
                   normalizedCookieDomain.endsWith('.' + normalizedWhitelistDomain);
          });
        });

        // Save whitelisted cookies to restore on next startup
        if (whitelistedCookies.length > 0) {
          console.log(`Saving ${whitelistedCookies.length} whitelisted cookies for next startup`);
          store.set('savedCookies', whitelistedCookies);
        }

        // Clear all cookies and cache with safer approach
        try {
          // Clear cookies first
          await ses.clearStorageData({ storages: ['cookies'] });

          // Clear cache
          await ses.clearCache();

          // Clear other storages one by one to isolate potential issues
          const storageTypes = ['appcache', 'filesystem', 'indexdb', 'localstorage', 'shadercache', 'websql', 'cachestorage'];
          for (const storageType of storageTypes) {
            try {
              await ses.clearStorageData({ storages: [storageType] });
            } catch (storageErr) {
              console.warn(`Warning: Could not clear ${storageType} storage on exit:`, storageErr);
              // Continue with other storage types even if one fails
            }
          }

          // Try to clear service workers separately with error handling
          try {
            await ses.clearStorageData({ storages: ['serviceworkers'] });
          } catch (swErr) {
            console.warn('Warning: Could not clear service worker storage on exit:', swErr);
            // Service worker errors are expected in some cases, so we just log and continue
          }
        } catch (clearErr) {
          console.error('Error during storage clearing on exit:', clearErr);
        }

        // Now quit the app - use a slightly longer timeout to ensure clearing completes
        setTimeout(() => {
          console.log('Quitting application after cookie processing');
          app.exit(0); // Use exit instead of quit to force quit
        }, 300);

        // Set a timeout to reset the flag if the app somehow doesn't quit
        setTimeout(() => {
          console.log('Quit timeout reached, resetting quit flag');
          isQuitting = false;
        }, 5000);
      } else {
        // No whitelist and save cookies disabled, clear everything
        console.log('Clearing all cookies on exit');
        const ses = session.defaultSession;

        try {
          // Clear cookies first
          await ses.clearStorageData({ storages: ['cookies'] });

          // Clear cache
          await ses.clearCache();

          // Clear other storages one by one to isolate potential issues
          const storageTypes = ['appcache', 'filesystem', 'indexdb', 'localstorage', 'shadercache', 'websql', 'cachestorage'];
          for (const storageType of storageTypes) {
            try {
              await ses.clearStorageData({ storages: [storageType] });
            } catch (storageErr) {
              console.warn(`Warning: Could not clear ${storageType} storage on exit:`, storageErr);
              // Continue with other storage types even if one fails
            }
          }

          // Try to clear service workers separately with error handling
          try {
            await ses.clearStorageData({ storages: ['serviceworkers'] });
          } catch (swErr) {
            console.warn('Warning: Could not clear service worker storage on exit:', swErr);
            // Service worker errors are expected in some cases, so we just log and continue
          }
        } catch (clearErr) {
          console.error('Error during storage clearing on exit:', clearErr);
        }

        // Force quit the app
        setTimeout(() => {
          console.log('Quitting application after cookie processing (no whitelist case)');
          app.exit(0); // Use exit instead of quit to force quit
        }, 300);
      }
    } catch (e) {
      console.error('Error handling cookies on exit:', e);

      // Reset the quitting flag after error
      setTimeout(() => {
        console.log('Resetting quit flag after error');
        isQuitting = false;
      }, 1000);
    }
  });

  // Set up download handler for webview sessions
  session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
    // Get the content type
    const contentTypeHeader = details.responseHeaders['content-type'] || details.responseHeaders['Content-Type'];
    const contentType = contentTypeHeader ? contentTypeHeader[0] : '';

    // Check if this is a PDF
    const isPDF = contentType.includes('application/pdf') || details.url.toLowerCase().endsWith('.pdf');

    // If it's a PDF, set Content-Disposition to attachment to force download
    if (isPDF) {
      console.log('PDF detected, setting Content-Disposition to attachment:', details.url);
      details.responseHeaders['Content-Disposition'] = ['attachment'];
    }

    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': ["default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: *"]
      }
    });
  });

  // Configure PDF handling in the session
  session.defaultSession.setPermissionRequestHandler((webContents, permission, callback) => {
    // Always allow these permissions
    callback(true);
  });

  app.on("activate", () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })

  // REMOVED: webRequest.onBeforeRequest handler for problematic domains
  // This was causing conflicts with webview-level handling, leading to ERR_ABORTED errors
  // and GUEST_VIEW_MANAGER_CALL issues. Problematic domains are now handled consistently
  // at the webview level in webview-handler.js
})

// Quit when all windows are closed
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") app.quit()
})

// Handle navigation requests
ipcMain.on("navigate", (event, url) => {
  event.reply("navigate-to", url)
})

// Handle new tab requests
ipcMain.on("new-tab", (event, url) => {
  event.reply("create-new-tab", url || "https://www.google.com")
})

// Handle bookmark creation
ipcMain.on("add-bookmark", (event, bookmark) => {
  event.reply("bookmark-added", bookmark)
})

// Clear browsing data
ipcMain.on("clear-data", async (event) => {
  const ses = session.defaultSession;

  try {
    // Get settings
    const settings = store ? store.get('settings') : {};
    const saveCookies = settings && settings.saveCookies === 'true';
    const cookieWhitelist = settings && settings.cookieWhitelist ? settings.cookieWhitelist : [];

    if (saveCookies) {
      // If save cookies is enabled, don't clear anything
      console.log('Save cookies enabled, skipping data clearing');
    } else if (cookieWhitelist && cookieWhitelist.length > 0) {
      // If we have a whitelist, we need to save those cookies and clear everything else
      console.log('Using cookie whitelist:', cookieWhitelist);

      // Get all cookies
      const allCookies = await ses.cookies.get({});

      // Filter cookies that match the whitelist
      const whitelistedCookies = allCookies.filter(cookie => {
        const domain = cookie.domain.startsWith('.') ? cookie.domain.substring(1) : cookie.domain;

        // Check if this cookie's domain matches any whitelist entry
        return cookieWhitelist.some(whitelistDomain => {
          // Normalize domains for comparison
          const normalizedWhitelistDomain = whitelistDomain.toLowerCase().trim();
          const normalizedCookieDomain = domain.toLowerCase().trim();

          // Check for exact match or subdomain match
          return normalizedCookieDomain === normalizedWhitelistDomain ||
                 normalizedCookieDomain.endsWith('.' + normalizedWhitelistDomain);
        });
      });

      // Clear data with safer approach to avoid service worker storage errors
      try {
        // Clear cookies first
        await ses.clearStorageData({ storages: ['cookies'] });

        // Clear cache
        await ses.clearCache();

        // Clear other storages one by one to isolate potential issues
        const storageTypes = ['appcache', 'filesystem', 'indexdb', 'localstorage', 'shadercache', 'websql', 'cachestorage'];
        for (const storageType of storageTypes) {
          try {
            await ses.clearStorageData({ storages: [storageType] });
          } catch (storageErr) {
            console.warn(`Warning: Could not clear ${storageType} storage:`, storageErr);
            // Continue with other storage types even if one fails
          }
        }

        // Try to clear service workers separately with error handling
        try {
          await ses.clearStorageData({ storages: ['serviceworkers'] });
        } catch (swErr) {
          console.warn('Warning: Could not clear service worker storage:', swErr);
          // Service worker errors are expected in some cases, so we just log and continue
        }
      } catch (clearErr) {
        console.error('Error during storage clearing:', clearErr);
      }

      // Restore whitelisted cookies
      console.log(`Restoring ${whitelistedCookies.length} whitelisted cookies`);
      for (const cookie of whitelistedCookies) {
        try {
          // Format cookie for setting
          const cookieDetails = {
            url: (cookie.secure ? 'https://' : 'http://') + (cookie.domain.startsWith('.') ? 'www' + cookie.domain : cookie.domain) + cookie.path,
            name: cookie.name,
            value: cookie.value,
            domain: cookie.domain,
            path: cookie.path,
            secure: cookie.secure,
            httpOnly: cookie.httpOnly,
            expirationDate: cookie.expirationDate,
            sameSite: cookie.sameSite
          };

          await ses.cookies.set(cookieDetails);
        } catch (err) {
          console.error('Error restoring cookie:', err);
        }
      }
    } else {
      // No whitelist and save cookies disabled, clear everything with safer approach
      console.log('Clearing all browsing data');

      // Clear data with safer approach to avoid service worker storage errors
      try {
        // Clear cookies first
        await ses.clearStorageData({ storages: ['cookies'] });

        // Clear cache
        await ses.clearCache();

        // Clear other storages one by one to isolate potential issues
        const storageTypes = ['appcache', 'filesystem', 'indexdb', 'localstorage', 'shadercache', 'websql', 'cachestorage'];
        for (const storageType of storageTypes) {
          try {
            await ses.clearStorageData({ storages: [storageType] });
          } catch (storageErr) {
            console.warn(`Warning: Could not clear ${storageType} storage:`, storageErr);
            // Continue with other storage types even if one fails
          }
        }

        // Try to clear service workers separately with error handling
        try {
          await ses.clearStorageData({ storages: ['serviceworkers'] });
        } catch (swErr) {
          console.warn('Warning: Could not clear service worker storage:', swErr);
          // Service worker errors are expected in some cases, so we just log and continue
        }
      } catch (clearErr) {
        console.error('Error during storage clearing:', clearErr);
      }
    }
  } catch (e) {
    console.error('Error clearing browsing data:', e);
    // Fallback to clearing everything on error, but with safer approach
    try {
      // Clear cookies first
      await ses.clearStorageData({ storages: ['cookies'] });

      // Clear cache
      await ses.clearCache();

      // Try other storages individually
      const storageTypes = ['appcache', 'filesystem', 'indexdb', 'localstorage', 'shadercache', 'websql', 'cachestorage', 'serviceworkers'];
      for (const storageType of storageTypes) {
        try {
          await ses.clearStorageData({ storages: [storageType] });
        } catch (storageErr) {
          console.warn(`Warning: Could not clear ${storageType} storage:`, storageErr);
        }
      }
    } catch (finalErr) {
      console.error('Final error during fallback storage clearing:', finalErr);
    }
  }

  event.reply("data-cleared");
});

// Handle time limit setting
ipcMain.on("set-time-limit", (event, { domain, minutes }) => {
  if (store) {
    const timeLimits = store.get('timeLimits') || {};
    timeLimits[domain] = minutes;
    store.set('timeLimits', timeLimits);
  }
});

// Handle application restart
ipcMain.on("restart-app", (event) => {
  if (!isRestarting) {
    isRestarting = true;
    console.log("Restarting application...");
    app.relaunch();
    app.exit(0);
  }
});

// Handle download whitelist updates
ipcMain.on("update-download-whitelist", (event, whitelist) => {
  if (store) {
    console.log("Updating download whitelist:", whitelist);
    const settings = store.get('settings') || {};
    settings.downloadWhitelist = JSON.stringify(whitelist);
    store.set('settings', settings);
    event.reply("download-whitelist-updated");
  }
});

// Handle download blocking setting
ipcMain.on("set-block-downloads", (event, blockDownloads) => {
  if (store) {
    console.log("Setting block downloads:", blockDownloads);
    const settings = store.get('settings') || {};
    settings.blockDownloads = blockDownloads ? 'true' : 'false';
    store.set('settings', settings);
    event.reply("block-downloads-updated");
  }
});

// Handle save settings
ipcMain.on("save-settings", (event, settings) => {
  if (store) {
    console.log("Saving settings:", settings);
    // Merge with existing settings
    const existingSettings = store.get('settings') || {};
    const newSettings = { ...existingSettings, ...settings };
    store.set('settings', newSettings);
    event.reply("settings-saved");
  }
});
