const { setupWebview, createWebview } = require('./webview-handler');
const { shell } = require('electron');

const urlInput = document.getElementById('url-input');
const backButton = document.getElementById('back');
const forwardButton = document.getElementById('forward');
const reloadButton = document.getElementById('reload');
const goButton = document.getElementById('go');
const newTabButton = document.getElementById('new-tab-btn');
const addBookmarkButton = document.getElementById('add-bookmark');
const tabsContainer = document.getElementById('tabs');
const bookmarksContainer = document.getElementById('bookmarks');
const webviewContainer = document.getElementById('webview-container');
const settingsButton = document.getElementById('settings');
const settingsModal = document.getElementById('settings-modal');
const closeSettingsBtn = document.getElementById('close-settings');
const settingsTabs = document.querySelectorAll('.settings-tab');
const settingsPanels = document.querySelectorAll('.settings-panel');
const bookmarksList = document.getElementById('bookmarks-list');
const newBookmarkTitle = document.getElementById('new-bookmark-title');
const newBookmarkUrl = document.getElementById('new-bookmark-url');
const addNewBookmarkBtn = document.getElementById('add-new-bookmark');

// Time limit elements
const timerDisplay = document.getElementById('timer-display');
const timerText = document.getElementById('timer-text');
const limitYoutube = document.getElementById('limit-youtube');
const limitFacebook = document.getElementById('limit-facebook');
const limitTotal = document.getElementById('limit-total');
const resetYoutube = document.getElementById('reset-youtube');
const resetFacebook = document.getElementById('reset-facebook');
const resetTotal = document.getElementById('reset-total');
const customDomain = document.getElementById('custom-domain');
const customMinutes = document.getElementById('custom-minutes');
const addCustomLimit = document.getElementById('add-custom-limit');
const customLimitsList = document.getElementById('custom-limits-list');
const saveTimeLimits = document.getElementById('save-time-limits');
const showTimer = document.getElementById('setting-show-timer');
const showRemainingTime = document.getElementById('setting-show-remaining-time');

// Dark mode elements
const darkModeToggle = document.getElementById('setting-dark-mode');
const saveGeneralSettings = document.getElementById('save-general-settings');

// Password protection elements
const passwordModal = document.getElementById('password-modal');
const passwordInput = document.getElementById('password-input');
const passwordPromptMessage = document.getElementById('password-prompt-message');
const submitPassword = document.getElementById('submit-password');
const cancelPassword = document.getElementById('cancel-password');
const passwordError = document.getElementById('password-error');
const settingPassword = document.getElementById('setting-password');
const savePassword = document.getElementById('save-password-new');
const deletePasswordBtn = document.getElementById('delete-password');
// Add reference for confirm password
let confirmSettingPassword = document.getElementById('confirm-setting-password');
if (!confirmSettingPassword) {
    // Create and insert the confirm password field if it doesn't exist
    confirmSettingPassword = document.createElement('input');
    confirmSettingPassword.type = 'password';
    confirmSettingPassword.id = 'confirm-setting-password';
    confirmSettingPassword.placeholder = 'Confirm password';
    confirmSettingPassword.style.flex = '1';
    confirmSettingPassword.style.padding = '10px';
    confirmSettingPassword.style.border = '1px solid #ccc';
    confirmSettingPassword.style.borderRadius = '4px';
    confirmSettingPassword.style.fontSize = '14px';
    // Insert after settingPassword
    const pwdRow = settingPassword.parentNode;
    pwdRow.insertBefore(confirmSettingPassword, pwdRow.children[1]);
}

// Debug check for elements
console.log('Delete password button found:', !!deletePasswordBtn);

// Tab management
let tabs = [];
let activeTabId = null;

// Function to set active tab
function setActiveTab(tabId) {
    // Hide all webviews
    tabs.forEach(tab => {
        tab.webview.style.display = 'none';
        tab.element.classList.remove('active');
    });

    // Show selected webview and mark tab as active
    const selectedTab = tabs.find(tab => tab.id === tabId);
    if (selectedTab) {
        selectedTab.webview.style.display = 'flex';
        selectedTab.element.classList.add('active');
        activeTabId = tabId;
    }
}

// Event listeners
document.getElementById('new-tab').addEventListener('click', () => {
    const tabId = Date.now().toString();
    createWebview('about:blank', tabId);
});

document.getElementById('go').addEventListener('click', () => {
    const url = document.getElementById('url-input').value;
    if (url) {
        if (!activeTabId) {
            const tabId = Date.now().toString();
            createWebview(url, tabId);
        } else {
            const activeTab = tabs.find(tab => tab.id === activeTabId);
            if (activeTab) {
                if (isProblematic(url)) {
                    shell.openExternal(url);
                } else {
                    activeTab.webview.src = url;
                }
            }
        }
    }
});

// Initialize with a new tab
document.getElementById('new-tab').click();

// Create a new tab
function createTab(url = 'https://www.google.com') {
    console.log('Creating new tab with URL:', url);

    try {
        // Create a more robust unique tab ID
        const tabId = 'tab-' + Date.now() + '-' + Math.floor(Math.random() * 1000000);
        console.log('Generated tab ID:', tabId);

        const tab = document.createElement('div');
        tab.className = 'tab';
        tab.dataset.id = tabId;
        tab.textContent = 'Tab';

        // Create close button
        const closeBtn = document.createElement('span');
        closeBtn.textContent = '×';
        closeBtn.style.marginLeft = '8px';
        closeBtn.style.cursor = 'pointer';
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            closeTab(tabId);
        });
        tab.appendChild(closeBtn);

        console.log('Tab element created');

        // Create webview
        console.log('Creating webview element');
        const webview = document.createElement('webview');
        webview.id = tabId;

        // Set webview preferences
        webview.setAttribute('preload', './preload.js');
        webview.setAttribute('partition', 'persist:main');
        webview.setAttribute('allowpopups', 'false'); // Disable popups for security
        webview.setAttribute('nodeintegration', 'false');
        webview.setAttribute('contextisolation', 'true');
        webview.setAttribute('sandbox', 'true');
        webview.setAttribute('webpreferences', 'backgroundThrottling=true');
        webview.setAttribute('webpreferences', 'frameRate=30');
        webview.setAttribute('webpreferences', 'spellcheck=false');
        webview.setAttribute('webpreferences', 'webSecurity=true');
        webview.setAttribute('webpreferences', 'allowRunningInsecureContent=false');

        // Handle new windows
        webview.addEventListener('new-window', (e) => {
            // Send message to main process to handle popup
            ipcRenderer.send('create-new-tab', e.url);
        });

        // DIRECT APPROACH: Block images at webview creation time
        console.log('CREATE WEBVIEW: Creating webview for URL:', url);

        // Check if image blocking is enabled
        const blockImagesEnabled = localStorage.getItem('blockImages') === 'true';
        const imageWhitelist = JSON.parse(localStorage.getItem('imageWhitelist') || '[]');

        // Extract domain from URL to check whitelist
        let domain = '';
        try {
            const urlObj = new URL(url);
            domain = urlObj.hostname;
        } catch (e) {
            console.error('Error extracting domain from URL:', e);
        }

        // Simple whitelist check
        let isWhitelisted = false;

        if (domain && imageWhitelist && imageWhitelist.length > 0) {
            const domainLower = domain.toLowerCase();

            for (let i = 0; i < imageWhitelist.length; i++) {
                const item = imageWhitelist[i];
                if (!item) continue;

                const itemLower = item.toLowerCase();

                // Simple exact match
                if (domainLower === itemLower) {
                    isWhitelisted = true;
                    break;
                }

                // Simple subdomain check
                if (domainLower.endsWith('.' + itemLower)) {
                    isWhitelisted = true;
                    break;
                }
            }
        }

        console.log('CREATE WEBVIEW: Domain:', domain, 'Image blocking enabled:', blockImagesEnabled, 'Is whitelisted:', isWhitelisted);

        // Set image loading preferences directly at creation time
        // This is equivalent to Android WebView's:
        // settings.loadsImagesAutomatically = false
        // settings.blockNetworkImage = true
        if (blockImagesEnabled && !isWhitelisted) {
            console.log('CREATE WEBVIEW: BLOCKING IMAGES for domain:', domain);

            // Disable images at the webview level
            webview.setAttribute('webpreferences', 'images=false');

            // Also set partition to isolate this webview's cache and settings
            // This helps prevent images from being loaded from cache
            webview.setAttribute('partition', 'noimages');
        } else {
            console.log('CREATE WEBVIEW: ENABLING IMAGES for domain:', domain);

            // Use default partition for normal image loading
            webview.setAttribute('partition', 'persist:default');
        }

        // ... script continues ... 
    } catch (e) {
        console.error('Error creating tab:', e);
    }
}

// Ensure all open functions and blocks are closed
// If you have more logic to add, place it above this line. 

const problematicDomains = ['aliexpress.com', 'chess.com'];
function isProblematic(url) {
    if (!url) return false;
    return problematicDomains.some(domain => url.includes(domain));
} 