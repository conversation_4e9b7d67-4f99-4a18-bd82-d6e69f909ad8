<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; frame-src 'self' https: http:;">
    <title>Electron Browser</title>
    <style>
        :root {
            /* Light mode colors */
            --bg-color: #ffffff;
            --text-color: #333333;
            --toolbar-bg: #f0f0f0;
            --toolbar-border: #e0e0e0;
            --tab-bg: #e9e9e9;
            --tab-active-bg: #ffffff;
            --tab-hover-bg: #f5f5f5;
            --tab-border: #d0d0d0;
            --tab-active-border: #4285f4;
            --url-bar-bg: #ffffff;
            --url-bar-border: #e0e0e0;
            --url-bar-focus-border: #4285f4;
            --button-bg: #4285f4;
            --button-hover-bg: #3367d6;
            --button-text: #ffffff;
            --webview-bg: #ffffff;
            --divider-color: #e0e0e0;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --modal-bg: #ffffff;
            --modal-overlay: rgba(0, 0, 0, 0.5);
            --titlebar-bg: #f0f0f0;
            --titlebar-text: #333333;
            --titlebar-button-hover: #e0e0e0;
            --titlebar-button-active: #d0d0d0;
            --titlebar-close-hover: #e81123;
            --titlebar-close-active: #f1707a;

            /* Font settings */
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            font-size: 14px;
        }

        /* Dark mode colors */
        body.dark-mode {
            --bg-color: #1e1e1e;
            --text-color: #e0e0e0;
            --toolbar-bg: #2d2d2d;
            --toolbar-border: #3d3d3d;
            --tab-bg: #252525;
            --tab-active-bg: #333333;
            --tab-hover-bg: #3a3a3a;
            --tab-border: #3d3d3d;
            --tab-active-border: #4285f4;
            --url-bar-bg: #333333;
            --url-bar-border: #444444;
            --url-bar-focus-border: #4285f4;
            --button-bg: #4285f4;
            --button-hover-bg: #3367d6;
            --button-text: #ffffff;
            --webview-bg: #1e1e1e;
            --divider-color: #3d3d3d;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --modal-bg: #23272b;
            --modal-overlay: rgba(0, 0, 0, 0.7);
            --titlebar-bg: #000000;
            --titlebar-text: #ffffff;
            --titlebar-button-hover: #333333;
            --titlebar-button-active: #444444;
            --titlebar-close-hover: #e81123;
            --titlebar-close-active: #f1707a;
        }

        /* Sleek dark mode for settings modal and its contents */
        body.dark-mode #settings-modal > div {
            background: #23272b !important;
            color: #e0e0e0 !important;
        }
        body.dark-mode .settings-tab {
            color: #e0e0e0 !important;
        }
        body.dark-mode .settings-tab.active {
            background-color: #23272b !important;
            border-bottom: 2px solid #4285f4 !important;
        }
        body.dark-mode .settings-panel {
            background: transparent !important;
            color: #e0e0e0 !important;
        }
        body.dark-mode .settings-panel h2,
        body.dark-mode .settings-panel h3,
        body.dark-mode .settings-panel h4 {
            color: #e0e0e0 !important;
        }
        body.dark-mode .settings-panel label,
        body.dark-mode .settings-panel p,
        body.dark-mode .settings-panel span {
            color: #cccccc !important;
        }
        body.dark-mode .settings-panel input[type="text"],
        body.dark-mode .settings-panel input[type="number"],
        body.dark-mode .settings-panel input[type="password"] {
            background: #23272b !important;
            color: #e0e0e0 !important;
            border: 1px solid #444 !important;
        }
        body.dark-mode .settings-panel input[type="checkbox"] {
            accent-color: #4285f4;
        }
        body.dark-mode .settings-panel button {
            background: #4285f4 !important;
            color: #fff !important;
            border: none !important;
        }
        body.dark-mode .settings-panel button:hover {
            background: #3367d6 !important;
        }
        body.dark-mode .settings-panel .whitelist-item {
            background: #23272b !important;
            color: #e0e0e0 !important;
            border: 1px solid #444 !important;
        }
        body.dark-mode .settings-panel .whitelist-item:hover {
            background: #2d2d2d !important;
        }
        body.dark-mode .settings-panel .bookmark-item {
            background: #23272b !important;
            color: #e0e0e0 !important;
            border-bottom: 1px solid #444 !important;
        }
        body.dark-mode .settings-panel .bookmark-item:hover {
            background: #2d2d2d !important;
        }
        body.dark-mode .settings-panel .bookmark-actions button {
            background: #333 !important;
            color: #fff !important;
            border: 1px solid #444 !important;
        }
        body.dark-mode .settings-panel .bookmark-actions button:hover {
            background: #4285f4 !important;
        }
        /* Feature boxes in settings modal */
        body.dark-mode .settings-panel > div[style*="background:#f5f5f5"],
        body.dark-mode .settings-panel > div[style*="background: #f5f5f5"],
        body.dark-mode .settings-panel > div[style*="background: #fff3cd"],
        body.dark-mode .settings-panel > div[style*="background:#fff3cd"] {
            background: #23272b !important;
            color: #e0e0e0 !important;
            border: 1px solid #444 !important;
        }
        /* Predefined limits boxes */
        body.dark-mode .settings-panel > div > div[style*="background:#f5f5f5"],
        body.dark-mode .settings-panel > div > div[style*="background: #f5f5f5"] {
            background: #23272b !important;
            color: #e0e0e0 !important;
            border: 1px solid #444 !important;
        }

        html, body {
            margin: 0;
            padding: 0;
            background: var(--bg-color);
            color: var(--text-color);
            width: 100%;
            height: 100vh;
            overflow: hidden;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 0; /* Important for proper flex sizing */
        }

        #titlebar {
            display: flex;
            align-items: center;
            height: 32px;
            background: var(--titlebar-bg);
            color: var(--titlebar-text);
            -webkit-app-region: drag;
            user-select: none;
        }

        #titlebar-menu {
            display: flex;
            align-items: center;
            margin-left: 10px;
        }

        .menu-item {
            position: relative;
            padding: 0 10px;
            height: 30px;
            display: flex;
            align-items: center;
            cursor: pointer;
            color: #fff;
        }

        .menu-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .dropdown-content {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background-color: #2d2d2d;
            min-width: 160px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.2);
            z-index: 1000;
        }

        .menu-item:hover .dropdown-content {
            display: block;
        }

        .dropdown-content a {
            color: #fff;
            padding: 8px 16px;
            text-decoration: none;
            display: block;
            font-size: 13px;
        }

        .dropdown-content a:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .dropdown-content hr {
            border: none;
            border-top: 1px solid #444;
            margin: 4px 0;
        }

        #titlebar-title {
            flex: 1;
            text-align: center;
            font-size: 12px;
            margin-left: 12px;
        }

        #titlebar-controls {
            display: flex;
            align-items: center;
            -webkit-app-region: no-drag;
        }

        .titlebar-button {
            width: 46px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            -webkit-app-region: no-drag;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .titlebar-button:hover {
            background-color: var(--titlebar-button-hover);
        }

        .titlebar-button:active {
            background-color: var(--titlebar-button-active);
        }

        #titlebar-close:hover {
            background-color: var(--titlebar-close-hover);
        }

        #titlebar-close:active {
            background-color: var(--titlebar-close-active);
        }

        #toolbar {
            background: var(--toolbar-bg);
            padding: 8px 12px;
            display: flex;
            gap: 8px;
            align-items: center;
            border-bottom: 1px solid var(--toolbar-border);
            box-shadow: 0 1px 3px var(--shadow-color);
            z-index: 10;
            transition: background-color 0.3s ease, border-color 0.3s ease;
        }

        #url-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid var(--url-bar-border);
            border-radius: 20px;
            background: var(--url-bar-bg);
            color: var(--text-color);
            font-size: 14px;
            outline: none;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px var(--shadow-color);
        }

        #url-input:focus {
            border-color: var(--url-bar-focus-border);
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }

        button {
            padding: 8px;
            background: var(--button-bg);
            border: none;
            border-radius: 50%;
            color: var(--button-text);
            cursor: pointer;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px var(--shadow-color);
        }

        button:hover {
            background: var(--button-hover-bg);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        #go {
            border-radius: 20px;
            width: auto;
            padding: 8px 16px;
        }

        #tabs {
            display: flex;
            padding: 0 8px;
            background: var(--toolbar-bg);
            height: 36px;
            align-items: flex-end;
            gap: 2px;
            transition: background-color 0.3s ease;
        }

        .tab {
            padding: 8px 12px;
            background: var(--tab-bg);
            border-radius: 8px 8px 0 0;
            cursor: pointer;
            font-size: 12px;
            max-width: 160px;
            height: 28px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: flex;
            align-items: center;
            color: var(--text-color);
            border: 1px solid var(--tab-border);
            border-bottom: none;
            transition: all 0.2s ease;
            position: relative;
            margin-right: 1px;
        }

        .tab:hover {
            background: var(--tab-hover-bg);
        }

        .tab.active {
            background: var(--tab-active-bg);
            height: 32px;
            z-index: 1;
            border-top: 2px solid var(--tab-active-border);
            font-weight: 500;
        }

        #bookmarks {
            display: flex;
            gap: 5px;
            padding: 8px;
            background: #f8f8f8;
            border-bottom: 1px solid #ddd;
            flex-wrap: wrap;
            align-items: center;
            min-height: 20px; /* Ensure minimum height even when empty */
        }

        /* Hide bookmarks bar when empty except for the title */
        #bookmarks:empty,
        #bookmarks:has(> span#bookmark-title:only-child) {
            display: none;
        }

        .bookmark {
            padding: 6px 12px;
            background: #e8e8e8;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            transition: background-color 0.2s;
        }

        .bookmark:hover {
            background: #d8d8d8;
        }

        .bookmark .favicon {
            width: 16px;
            height: 16px;
            margin-right: 6px;
        }

        .bookmark .remove {
            margin-left: 6px;
            font-size: 14px;
            opacity: 0.5;
            transition: opacity 0.2s;
        }

        .bookmark .remove:hover {
            opacity: 1;
        }

        #bookmark-title {
            font-weight: bold;
            margin-right: 10px;
            color: #555;
        }

        #webview-container {
            flex: 1;
            position: relative;
            background: var(--webview-bg);
            display: flex;
            flex-direction: column;
            min-height: 0; /* Important for proper flex sizing */
            transition: background-color 0.3s ease;
        }

        webview {
            width: 100%;
            height: 100%;
            border: none;
            flex: 1;
            display: flex;
            min-height: 0; /* Important for proper flex sizing */
            background: var(--webview-bg);
            transition: background-color 0.3s ease;
        }

        /* Settings Modal Styles */
        .settings-tab {
            padding: 12px 20px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: var(--text-color);
            position: relative;
        }

        .settings-tab:hover {
            background-color: var(--tab-hover-bg);
        }

        .settings-tab.active {
            background-color: var(--tab-active-bg);
            border-bottom: 2px solid var(--tab-active-border);
            font-weight: 500;
        }

        .bookmark-item {
            display: flex;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #eee;
        }

        .bookmark-item:hover {
            background-color: #f5f5f5;
        }

        .bookmark-favicon {
            width: 16px;
            height: 16px;
            margin-right: 10px;
        }

        .bookmark-title {
            flex: 1;
            font-weight: 500;
        }

        .bookmark-url {
            flex: 2;
            color: #666;
            margin: 0 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .bookmark-actions {
            display: flex;
            gap: 5px;
        }

        .bookmark-actions button {
            padding: 4px 8px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
        }

        .bookmark-actions button:hover {
            background: #e0e0e0;
        }

        /* Timer Display */
        #timer-display {
            position: fixed;
            top: 5px;
            left: 5px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            z-index: 1001;
            display: flex;
            align-items: center;
            gap: 5px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        #timer-display:hover {
            background: rgba(0, 0, 0, 0.9);
        }

        #timer-icon {
            font-size: 14px;
        }

        #timer-text {
            font-family: monospace;
        }

        /* --- Modern, sleek Time Limits tab styles --- */
        #panel-time-limits h2, #panel-time-limits h3 {
            font-size: 1.3em;
            font-weight: 700;
            margin-bottom: 12px;
        }
        #panel-time-limits > div {
            border-radius: 14px !important;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-bottom: 22px !important;
            padding: 18px 20px !important;
        }
        #panel-time-limits input[type="text"],
        #panel-time-limits input[type="number"],
        #panel-time-limits input[type="password"] {
            border-radius: 8px !important;
            padding: 12px 14px !important;
            font-size: 1em;
            margin-bottom: 8px;
            border: 1.5px solid #c0c0c0;
            background: #f7f8fa;
            transition: border 0.2s, background 0.2s;
        }
        #panel-time-limits input[type="text"]:focus,
        #panel-time-limits input[type="number"]:focus,
        #panel-time-limits input[type="password"]:focus {
            border: 1.5px solid #4285f4;
            background: #eaf1fb;
        }
        #panel-time-limits button,
        #panel-time-limits .edit-limit,
        #panel-time-limits .delete-limit {
            border-radius: 999px !important;
            padding: 10px 22px !important;
            font-size: 1em;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(66,133,244,0.08);
            margin: 4px 0 4px 8px;
            transition: background 0.2s, color 0.2s, box-shadow 0.2s;
        }
        #panel-time-limits button:active,
        #panel-time-limits .edit-limit:active,
        #panel-time-limits .delete-limit:active {
            box-shadow: 0 1px 4px rgba(66,133,244,0.18);
        }
        #panel-time-limits .edit-limit {
            background: #333 !important;
            color: #fff !important;
            border: 1px solid #444 !important;
        }
        #panel-time-limits .edit-limit:hover {
            background: #4285f4 !important;
        }
        #panel-time-limits .delete-limit {
            background: #d32f2f !important;
            color: #fff !important;
            border: 1px solid #b71c1c !important;
        }
        #panel-time-limits .delete-limit:hover {
            background: #b71c1c !important;
        }
        #panel-time-limits .custom-limits-list > div,
        #custom-limits-list > div {
            border-radius: 10px !important;
            margin-bottom: 10px;
            padding: 12px 16px !important;
            box-shadow: 0 1px 6px rgba(0,0,0,0.07);
        }
        #panel-time-limits label {
            font-size: 1em;
            font-weight: 500;
            margin-bottom: 6px;
        }
        #panel-time-limits input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #4285f4;
        }
        #panel-time-limits .settings-panel > div {
            margin-bottom: 18px !important;
        }
        /* Responsive for buttons in a row */
        #panel-time-limits .settings-panel button,
        #panel-time-limits .settings-panel .edit-limit,
        #panel-time-limits .settings-panel .delete-limit {
            display: inline-block;
            min-width: 90px;
        }
        /* --- Modern dark mode for Time Limits tab --- */
        body.dark-mode #panel-time-limits input[type="text"],
        body.dark-mode #panel-time-limits input[type="number"],
        body.dark-mode #panel-time-limits input[type="password"] {
            background: #23272b !important;
            color: #e0e0e0 !important;
            border: 1.5px solid #444 !important;
        }
        body.dark-mode #panel-time-limits input[type="text"]:focus,
        body.dark-mode #panel-time-limits input[type="number"]:focus,
        body.dark-mode #panel-time-limits input[type="password"]:focus {
            background: #232c3a !important;
            border: 1.5px solid #4285f4 !important;
        }
        body.dark-mode #panel-time-limits > div,
        body.dark-mode #panel-time-limits .custom-limits-list > div,
        body.dark-mode #custom-limits-list > div {
            background: #23272b !important;
            color: #e0e0e0 !important;
            border: 1px solid #444 !important;
            box-shadow: 0 2px 12px rgba(0,0,0,0.18);
        }
        body.dark-mode #panel-time-limits button,
        body.dark-mode #panel-time-limits .edit-limit,
        body.dark-mode #panel-time-limits .delete-limit {
            background: #4285f4 !important;
            color: #fff !important;
            border: none !important;
        }
        body.dark-mode #panel-time-limits button:hover,
        body.dark-mode #panel-time-limits .edit-limit:hover {
            background: #3367d6 !important;
        }
        body.dark-mode #panel-time-limits .delete-limit {
            background: #d32f2f !important;
            color: #fff !important;
            border: 1px solid #b71c1c !important;
        }
        body.dark-mode #panel-time-limits .delete-limit:hover {
            background: #b71c1c !important;
        }
        /* Modern placeholder color for dark mode */
        body.dark-mode #panel-time-limits input[type="text"]::placeholder,
        body.dark-mode #panel-time-limits input[type="number"]::placeholder,
        body.dark-mode #panel-time-limits input[type="password"]::placeholder {
            color: #888 !important;
            opacity: 1 !important;
        }

        /* --- Time Limits tab dark mode improvements --- */
        body.dark-mode #panel-time-limits > div[style*="background:#f5f5f5"],
        body.dark-mode #panel-time-limits > div[style*="background: #f5f5f5"],
        body.dark-mode #panel-time-limits > div[style*="background: #fff3cd"],
        body.dark-mode #panel-time-limits > div[style*="background:#fff3cd"] {
            background: #23272b !important;
            color: #e0e0e0 !important;
            border: 1px solid #444 !important;
        }
        body.dark-mode #panel-time-limits > div > div[style*="background:#f5f5f5"],
        body.dark-mode #panel-time-limits > div > div[style*="background: #f5f5f5"] {
            background: #23272b !important;
            color: #e0e0e0 !important;
            border: 1px solid #444 !important;
        }
        body.dark-mode #panel-time-limits input[type="text"],
        body.dark-mode #panel-time-limits input[type="number"] {
            background: #23272b !important;
            color: #e0e0e0 !important;
            border: 1px solid #444 !important;
        }
        body.dark-mode #panel-time-limits button {
            background: #4285f4 !important;
            color: #fff !important;
            border: none !important;
        }
        body.dark-mode #panel-time-limits button:hover {
            background: #3367d6 !important;
        }
        body.dark-mode #panel-time-limits .edit-limit,
        body.dark-mode #panel-time-limits .delete-limit {
            background: #333 !important;
            color: #fff !important;
            border: 1px solid #444 !important;
        }
        body.dark-mode #panel-time-limits .edit-limit:hover,
        body.dark-mode #panel-time-limits .delete-limit:hover {
            background: #4285f4 !important;
        }
        body.dark-mode #custom-limits-list > div {
            background: #23272b !important;
            color: #e0e0e0 !important;
            border: 1px solid #444 !important;
        }
        /* --- STRONG dark mode override for Time Limits tab feature boxes and inputs/buttons --- */
        body.dark-mode #panel-time-limits .settings-panel > div,
        body.dark-mode #panel-time-limits > div,
        body.dark-mode #panel-time-limits > div > div,
        body.dark-mode #panel-time-limits [style*="background:#f5f5f5"],
        body.dark-mode #panel-time-limits [style*="background: #f5f5f5"],
        body.dark-mode #panel-time-limits [style*="background:#fff3cd"],
        body.dark-mode #panel-time-limits [style*="background: #fff3cd"] {
            background: #23272b !important;
            color: #e0e0e0 !important;
            border: 1px solid #444 !important;
        }
        body.dark-mode #panel-time-limits input[type="text"],
        body.dark-mode #panel-time-limits input[type="number"] {
            background: #23272b !important;
            color: #e0e0e0 !important;
            border: 1px solid #444 !important;
        }
        body.dark-mode #panel-time-limits input[type="text"]::placeholder,
        body.dark-mode #panel-time-limits input[type="number"]::placeholder {
            color: #888 !important;
            opacity: 1 !important;
        }
        body.dark-mode #panel-time-limits button,
        body.dark-mode #panel-time-limits .edit-limit,
        body.dark-mode #panel-time-limits .delete-limit {
            background: #4285f4 !important;
            color: #fff !important;
            border: none !important;
        }
        body.dark-mode #panel-time-limits button:hover,
        body.dark-mode #panel-time-limits .edit-limit:hover,
        body.dark-mode #panel-time-limits .delete-limit:hover {
            background: #3367d6 !important;
        }
        body.dark-mode #panel-time-limits .edit-limit,
        body.dark-mode #panel-time-limits .delete-limit {
            background: #333 !important;
            color: #fff !important;
            border: 1px solid #444 !important;
        }
        body.dark-mode #panel-time-limits .edit-limit:hover,
        body.dark-mode #panel-time-limits .delete-limit:hover {
            background: #4285f4 !important;
        }
        body.dark-mode #custom-limits-list > div {
            background: #23272b !important;
            color: #e0e0e0 !important;
            border: 1px solid #444 !important;
        }
        /* Also fix the minutes label color */
        body.dark-mode #panel-time-limits span,
        body.dark-mode #panel-time-limits label {
            color: #cccccc !important;
        }

        /* --- Password Protection section: align and style buttons green --- */
        #panel-time-limits #save-password-new,
        #panel-time-limits #delete-password {
            display: inline-block;
            min-width: 120px;
            margin: 0 0 0 8px;
            vertical-align: middle;
            text-align: center;
        }
        #panel-time-limits #delete-password {
            background: #43b581 !important;
            color: #fff !important;
            border: none !important;
            box-shadow: 0 2px 8px rgba(67,181,129,0.08);
            font-weight: 600;
            border-radius: 999px !important;
            padding: 10px 22px !important;
            transition: background 0.2s, box-shadow 0.2s;
        }
        #panel-time-limits #delete-password:hover {
            background: #389e6b !important;
        }
        #panel-time-limits #save-password-new {
            background: #43b581 !important;
            color: #fff !important;
            border: none !important;
            box-shadow: 0 2px 8px rgba(67,181,129,0.08);
            font-weight: 600;
            border-radius: 999px !important;
            padding: 10px 22px !important;
            transition: background 0.2s, box-shadow 0.2s;
        }
        #panel-time-limits #save-password-new:hover {
            background: #389e6b !important;
        }
        /* Make sure the password button row is flex and aligned */
        #panel-time-limits .settings-panel > div > div[style*="display:flex;"] {
            display: flex !important;
            align-items: center !important;
            gap: 10px !important;
        }
        /* Center button text always */
        #panel-time-limits button {
            text-align: center !important;
        }
        /* --- Password Protection: align input and buttons in a row --- */
        #panel-time-limits .password-row {
            display: flex !important;
            align-items: center !important;
            gap: 12px !important;
            margin-bottom: 10px;
        }
        #panel-time-limits #setting-password {
            flex: 1 1 0%;
            min-width: 0;
        }
        #panel-time-limits #save-password-new,
        #panel-time-limits #delete-password {
            white-space: nowrap !important;
            min-width: 140px;
            margin: 0;
        }
        /* Prevent button text from wrapping */
        #panel-time-limits button,
        #panel-time-limits .edit-limit,
        #panel-time-limits .delete-limit {
            white-space: nowrap !important;
        }
        /* --- Save Changes button: green and right-aligned --- */
        #panel-time-limits #save-time-limits {
            background: #43b581 !important;
            color: #fff !important;
            border: none !important;
            box-shadow: 0 2px 8px rgba(67,181,129,0.08);
            font-weight: 600;
            border-radius: 999px !important;
            padding: 10px 32px !important;
            transition: background 0.2s, box-shadow 0.2s;
            float: right;
            margin-right: 8px;
        }
        #panel-time-limits #save-time-limits:hover {
            background: #389e6b !important;
        }
        /* Ensure Save Changes row is clear and right-aligned */
        #panel-time-limits .settings-panel > div[style*="text-align:right"] {
            text-align: right !important;
            clear: both;
        }

        /* --- Modern, sleek style for ALL settings tabs --- */
        .settings-panel h2, .settings-panel h3 {
            font-size: 1.3em;
            font-weight: 700;
            margin-bottom: 12px;
        }
        .settings-panel > div {
            border-radius: 14px !important;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            margin-bottom: 22px !important;
            padding: 18px 20px !important;
        }
        .settings-panel input[type="text"],
        .settings-panel input[type="number"],
        .settings-panel input[type="password"] {
            border-radius: 8px !important;
            padding: 12px 14px !important;
            font-size: 1em;
            margin-bottom: 8px;
            border: 1.5px solid #c0c0c0;
            background: #f7f8fa;
            transition: border 0.2s, background 0.2s;
        }
        .settings-panel input[type="text"]:focus,
        .settings-panel input[type="number"]:focus,
        .settings-panel input[type="password"]:focus {
            border: 1.5px solid #43b581;
            background: #eafbf2;
        }
        .settings-panel button,
        .settings-panel .edit-limit,
        .settings-panel .delete-limit {
            border-radius: 999px !important;
            padding: 10px 22px !important;
            font-size: 1em;
            font-weight: 600;
            box-shadow: 0 2px 8px rgba(67,181,129,0.08);
            margin: 4px 0 4px 8px;
            transition: background 0.2s, color 0.2s, box-shadow 0.2s;
            background: #43b581 !important;
            color: #fff !important;
            border: none !important;
            white-space: nowrap !important;
        }
        .settings-panel button:hover,
        .settings-panel .edit-limit:hover {
            background: #389e6b !important;
        }
        .settings-panel .delete-limit {
            background: #d32f2f !important;
            color: #fff !important;
            border: 1px solid #b71c1c !important;
        }
        .settings-panel .delete-limit:hover {
            background: #b71c1c !important;
        }
        .settings-panel .custom-limits-list > div,
        #custom-limits-list > div {
            border-radius: 10px !important;
            margin-bottom: 10px;
            padding: 12px 16px !important;
            box-shadow: 0 1px 6px rgba(0,0,0,0.07);
        }
        .settings-panel label {
            font-size: 1em;
            font-weight: 500;
            margin-bottom: 6px;
        }
        .settings-panel input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #43b581;
        }
        .settings-panel > div {
            margin-bottom: 18px !important;
        }
        /* Responsive for buttons in a row */
        .settings-panel button,
        .settings-panel .edit-limit,
        .settings-panel .delete-limit {
            display: inline-block;
            min-width: 90px;
        }
        /* Save/primary action buttons right-aligned */
        .settings-panel > div[style*="text-align:right"] {
            text-align: right !important;
            clear: both;
        }
        .settings-panel > div[style*="text-align:right"] button {
            background: #43b581 !important;
            color: #fff !important;
            border: none !important;
            box-shadow: 0 2px 8px rgba(67,181,129,0.08);
            font-weight: 600;
            border-radius: 999px !important;
            padding: 10px 32px !important;
            transition: background 0.2s, box-shadow 0.2s;
            float: right;
            margin-right: 8px;
        }
        .settings-panel > div[style*="text-align:right"] button:hover {
            background: #389e6b !important;
        }
        /* --- Modern dark mode for ALL settings tabs --- */
        body.dark-mode .settings-panel input[type="text"],
        body.dark-mode .settings-panel input[type="number"],
        body.dark-mode .settings-panel input[type="password"] {
            background: #23272b !important;
            color: #e0e0e0 !important;
            border: 1.5px solid #444 !important;
        }
        body.dark-mode .settings-panel input[type="text"]:focus,
        body.dark-mode .settings-panel input[type="number"]:focus,
        body.dark-mode .settings-panel input[type="password"]:focus {
            background: #232c3a !important;
            border: 1.5px solid #43b581 !important;
        }
        body.dark-mode .settings-panel > div,
        body.dark-mode .settings-panel .custom-limits-list > div,
        body.dark-mode #custom-limits-list > div {
            background: #23272b !important;
            color: #e0e0e0 !important;
            border: 1px solid #444 !important;
            box-shadow: 0 2px 12px rgba(0,0,0,0.18);
        }
        body.dark-mode .settings-panel button,
        body.dark-mode .settings-panel .edit-limit,
        body.dark-mode .settings-panel .delete-limit {
            background: #43b581 !important;
            color: #fff !important;
            border: none !important;
        }
        body.dark-mode .settings-panel button:hover,
        body.dark-mode .settings-panel .edit-limit:hover {
            background: #389e6b !important;
        }
        body.dark-mode .settings-panel .delete-limit {
            background: #d32f2f !important;
            color: #fff !important;
            border: 1px solid #b71c1c !important;
        }
        body.dark-mode .settings-panel .delete-limit:hover {
            background: #b71c1c !important;
        }
        /* Modern placeholder color for dark mode */
        body.dark-mode .settings-panel input[type="text"]::placeholder,
        body.dark-mode .settings-panel input[type="number"]::placeholder,
        body.dark-mode .settings-panel input[type="password"]::placeholder {
            color: #888 !important;
            opacity: 1 !important;
        }
        /* --- Fix Save Changes button vertical alignment and height --- */
        .settings-panel > div[style*="text-align:right"] button {
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            height: 48px !important;
            min-height: 48px !important;
            line-height: 1.1 !important;
            padding: 0 56px !important;
            font-size: 1.05em !important;
            font-weight: 600 !important;
            box-sizing: border-box !important;
        }
        /* --- Make Save Changes button in Time Limits tab wide and consistent --- */
        #panel-time-limits #save-time-limits {
            display: inline-flex !important;
            align-items: center !important;
            justify-content: center !important;
            height: 48px !important;
            min-height: 48px !important;
            line-height: 1.1 !important;
            padding: 0 56px !important;
            font-size: 1.05em !important;
            font-weight: 600 !important;
            box-sizing: border-box !important;
        }
    </style>
</head>
<body>
    <div id="titlebar">
        <div id="titlebar-menu">
            <div class="menu-item">
                <span>File</span>
                <div class="dropdown-content">
                    <a href="#" id="new-tab">New Tab</a>
                    <a href="#" id="new-window">New Window</a>
                    <a href="#" id="open-file">Open File</a>
                    <hr>
                    <a href="#" id="exit">Exit</a>
                </div>
            </div>
            <div class="menu-item">
                <span>Edit</span>
                <div class="dropdown-content">
                    <a href="#" id="undo">Undo</a>
                    <a href="#" id="redo">Redo</a>
                    <hr>
                    <a href="#" id="cut">Cut</a>
                    <a href="#" id="copy">Copy</a>
                    <a href="#" id="paste">Paste</a>
                </div>
            </div>
            <div class="menu-item">
                <span>View</span>
                <div class="dropdown-content">
                    <a href="#" id="zoom-in">Zoom In</a>
                    <a href="#" id="zoom-out">Zoom Out</a>
                    <a href="#" id="zoom-reset">Reset Zoom</a>
                </div>
            </div>
        </div>
        <div id="titlebar-title">Electron Browser</div>
        <div id="titlebar-controls">
            <div class="titlebar-button" id="titlebar-minimize">─</div>
            <div class="titlebar-button" id="titlebar-maximize">□</div>
            <div class="titlebar-button" id="titlebar-close">×</div>
        </div>
    </div>
    <div id="timer-display">
        <span id="timer-icon">⏱</span>
        <span id="timer-text">00:00</span>
    </div>

    <!-- Download notification - improved visibility with percentage display -->
    <div id="download-notification" style="display: none; position: fixed; bottom: 20px; right: 20px; background-color: #333; color: white; padding: 15px; border-radius: 5px; box-shadow: 0 4px 20px rgba(0,0,0,0.4); z-index: 9999; min-width: 300px; max-width: 400px;">
        <div style="display: flex; align-items: center; margin-bottom: 10px;">
            <span style="font-size: 24px; margin-right: 15px;">📥</span>
            <div style="flex-grow: 1;">
                <div id="download-filename" style="font-weight: bold; margin-bottom: 8px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; font-size: 14px;"></div>
                <div id="download-status" style="font-size: 13px;"></div>
            </div>
            <button id="close-download-notification" style="background: none; border: none; color: white; cursor: pointer; font-size: 20px; padding: 5px;">×</button>
        </div>
        <div id="download-progress-container" style="width: 100%; height: 20px; background-color: #555; border-radius: 4px; overflow: hidden; margin-top: 5px; position: relative;">
            <div id="download-progress-bar" style="height: 100%; width: 0%; background-color: #4CAF50; transition: width 0.3s; position: relative; display: flex; align-items: center; justify-content: center;"></div>
        </div>
    </div>

    <!-- Download history panel -->
    <div id="download-history-panel" style="display: none; position: fixed; top: 60px; right: 10px; background-color: #222; color: white; border-radius: 8px; box-shadow: 0 5px 25px rgba(0,0,0,0.5); z-index: 9998; width: 350px; max-height: 500px; overflow: hidden;">
        <div style="display: flex; align-items: center; justify-content: space-between; padding: 15px; border-bottom: 1px solid #444;">
            <h3 style="margin: 0; font-size: 16px;">Download History</h3>
            <button id="clear-download-history" style="background: none; border: none; color: #aaa; cursor: pointer; font-size: 13px; padding: 5px 10px; border-radius: 4px; transition: all 0.2s;">Clear All</button>
        </div>
        <div id="download-history-list" style="max-height: 400px; overflow-y: auto; padding: 0;">
            <div style="padding: 20px; text-align: center; color: #888; font-size: 14px;">No downloads yet</div>
        </div>
        <div style="padding: 10px 15px; border-top: 1px solid #444; font-size: 12px; color: #888; text-align: center;">
            Download history is cleared when browser is closed
        </div>
    </div>
    <div id="toolbar">
        <button id="back">←</button>
        <button id="forward">→</button>
        <button id="reload">↻</button>
        <input type="text" id="url-input" placeholder="Enter URL">
        <button id="go">Go</button>
        <button id="new-tab-btn">+</button>
        <button id="add-bookmark">★</button>
        <button id="settings">⚙️</button>
        <button id="test-download" title="Download History">📥</button>
    </div>
    <div id="tabs"></div>
    <!-- Removed bookmark bar to prevent blocking website content -->
    <div id="webview-container">
        <!-- Webviews will be created dynamically -->
    </div>

    <!-- Settings Modal -->
    <div id="settings-modal" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:var(--modal-overlay); z-index:1000; transition:background 0.3s ease;">
        <div style="position:relative; width:80%; max-width:800px; margin:50px auto; background:var(--modal-bg); color:var(--text-color); border-radius:12px; box-shadow:0 0 30px rgba(0,0,0,0.5); overflow:hidden; transition:background-color 0.3s ease, color 0.3s ease;">
            <div style="display:flex; background:var(--toolbar-bg); border-bottom:1px solid var(--divider-color); transition:background-color 0.3s ease, border-color 0.3s ease;">
                <div class="settings-tab active" data-tab="bookmarks">Bookmarks</div>
                <div class="settings-tab" data-tab="time-limits">Time Limits</div>
                <div class="settings-tab" data-tab="parental-control">Parental Control</div>
                <div class="settings-tab" data-tab="general">General</div>
                <div class="settings-tab" data-tab="websites-cookies">Websites & Cookies</div>
                <div class="settings-tab" data-tab="advanced">Advanced</div>
                <div style="margin-left:auto; padding:10px; cursor:pointer; color:var(--text-color); font-size:18px; transition:color 0.3s ease;" id="close-settings">×</div>
            </div>
            <div id="settings-content" style="padding:20px; max-height:70vh; overflow:auto; transition:background-color 0.3s ease, color 0.3s ease;">
                <!-- Bookmarks Panel -->
                <div class="settings-panel active" id="panel-bookmarks">
                    <h2>Manage Bookmarks</h2>
                    <div id="bookmarks-list" style="margin:20px 0;"></div>
                    <div style="margin-top:20px;">
                        <h3>Add New Bookmark</h3>
                        <div style="display:flex; gap:10px; margin-top:10px;">
                            <input type="text" id="new-bookmark-title" placeholder="Title" style="flex:1; padding:8px;">
                            <input type="text" id="new-bookmark-url" placeholder="URL (e.g., https://example.com)" style="flex:2; padding:8px;">
                            <button id="add-new-bookmark" style="padding:8px 16px;">Add</button>
                        </div>
                    </div>
                </div>

                <!-- Time Limits Panel -->
                <div class="settings-panel" id="panel-time-limits" style="display:none;">
                    <h2>Time Limits</h2>
                    <p>Set daily time limits for websites. All timers reset at midnight (00:00).</p>

                    <div style="margin:20px 0;">
                        <h3>Predefined Limits</h3>
                        <div style="display:flex; flex-direction:column; gap:10px; margin-top:10px;">
                            <div style="display:flex; align-items:center; justify-content:space-between; padding:10px; background:#f5f5f5; border-radius:4px;">
                                <div>
                                    <strong>YouTube</strong>
                                    <div style="color:#666; font-size:12px;">youtube.com</div>
                                </div>
                                <div>
                                    <input type="number" id="limit-youtube" value="60" min="1" max="1440" style="width:60px; text-align:center;"> minutes
                                    <button id="reset-youtube" style="margin-left:10px;">Reset</button>
                                </div>
                            </div>

                            <div style="display:flex; align-items:center; justify-content:space-between; padding:10px; background:#f5f5f5; border-radius:4px;">
                                <div>
                                    <strong>Facebook</strong>
                                    <div style="color:#666; font-size:12px;">facebook.com</div>
                                </div>
                                <div>
                                    <input type="number" id="limit-facebook" value="25" min="1" max="1440" style="width:60px; text-align:center;"> minutes
                                    <button id="reset-facebook" style="margin-left:10px;">Reset</button>
                                </div>
                            </div>

                            <div style="display:flex; align-items:center; justify-content:space-between; padding:10px; background:#f5f5f5; border-radius:4px;">
                                <div>
                                    <strong>Total Browser Usage</strong>
                                    <div style="color:#666; font-size:12px;">All websites combined</div>
                                </div>
                                <div>
                                    <input type="number" id="limit-total" value="130" min="1" max="1440" style="width:60px; text-align:center;"> minutes
                                    <button id="reset-total" style="margin-left:10px;">Reset</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div style="margin:20px 0;">
                        <h3>Custom Website Limit</h3>
                        <div style="display:flex; gap:10px; margin-top:10px;">
                            <input type="text" id="custom-domain" placeholder="Domain (e.g., twitter.com)" style="flex:2; padding:8px;">
                            <input type="number" id="custom-minutes" placeholder="Minutes" min="1" max="1440" style="flex:1; padding:8px;">
                            <button id="add-custom-limit" style="padding:8px 16px;">Add Limit</button>
                        </div>

                        <div id="custom-limits-list" style="margin-top:15px;"></div>
                    </div>

                    <div style="margin:20px 0;">
                        <h3>Password Protection</h3>
                        <div style="margin-bottom:20px;">
                            <label style="display:block; margin-bottom:5px;">Set Password for Time Limits:</label>
                            <div style="display:flex; gap:10px; margin-bottom:10px;">
                                <input type="password" id="setting-password" placeholder="Set password for time limits" style="flex:1; padding:10px; border:1px solid #ccc; border-radius:4px; font-size:14px;">
                                <input type="password" id="confirm-setting-password" placeholder="Confirm password" style="flex:1; padding:10px; border:1px solid #ccc; border-radius:4px; font-size:14px;">
                                <button id="save-password-new" style="padding:10px 16px; background:#4285f4; color:white; border:none; border-radius:4px; cursor:pointer; white-space:nowrap;">Save Password</button>
                            </div>
                            <div style="margin-top:10px;">
                                <button id="delete-password" style="padding:8px 16px; background:#f44336; color:white; border:none; border-radius:4px; cursor:pointer;">Remove Password Protection</button>
                                <div id="password-status" style="margin-top:10px; font-weight:bold;"></div>
                            </div>
                            <p style="color:#666; font-size:12px; margin-top:10px;">
                                This password will be required to access time limit settings and make changes.
                                You can set a new password or remove the existing one using the buttons above.
                            </p>
                        </div>
                    </div>

                    <div style="margin:20px 0; text-align:right;">
                        <button id="save-time-limits" style="padding:10px 20px; background:#4285f4; color:white; border:none; border-radius:4px; cursor:pointer;">Save Changes</button>
                    </div>
                </div>

                <!-- Parental Control Panel -->
                <div class="settings-panel" id="panel-parental-control" style="display:none;">
                    <h2>Parental Control</h2>

                    <!-- Image Blocking -->
                    <div style="margin:20px 0;">
                        <h3>Image Blocking</h3>
                        <div style="display:flex; align-items:center; margin-bottom:10px;">
                            <label style="display:flex; align-items:center; cursor:pointer;">
                                <input type="checkbox" id="block-images" style="margin-right:10px;">
                                <span>Block all images on websites</span>
                            </label>
                        </div>
                        <div style="margin-top:15px;">
                            <h4>Image Blocking Whitelist</h4>
                            <p style="color:#666; font-size:12px; margin-bottom:10px;">
                                Add domains where images should still be displayed (e.g., google.com)
                            </p>
                            <div style="display:flex; gap:10px; margin-top:10px;">
                                <input type="text" id="image-whitelist-domain" placeholder="Domain (e.g., example.com)" style="flex:1; padding:8px;">
                                <button id="add-image-whitelist" style="padding:8px 16px; background:#4285f4; color:white; border:none; border-radius:4px; cursor:pointer;">Add</button>
                            </div>
                            <div id="image-whitelist" style="margin-top:10px; max-height:150px; overflow-y:auto;"></div>
                        </div>
                    </div>

                    <!-- Video Blocking -->
                    <div style="margin:20px 0; padding-top:10px; border-top:1px solid #eee;">
                        <h3>Video Blocking</h3>
                        <div style="background:#fff3cd; padding:10px; border-radius:4px; margin-bottom:15px; font-size:12px; color:#856404; border:1px solid #ffeaa7;">
                            <strong>ℹ️ Video blocking functionality is currently disabled.</strong><br>
                            Settings can be configured and saved, but no actual video blocking will occur. The framework is preserved for future implementation.
                        </div>
                        <div style="display:flex; align-items:center; margin-bottom:10px;">
                            <label style="display:flex; align-items:center; cursor:pointer;">
                                <input type="checkbox" id="block-videos" style="margin-right:10px;">
                                <span>Block all videos on websites (currently disabled)</span>
                            </label>
                        </div>
                        <div style="margin-top:15px;">
                            <h4>Video Blocking Whitelist</h4>
                            <p style="color:#666; font-size:12px; margin-bottom:10px;">
                                Add domains where videos should still be allowed (e.g., youtube.com)
                            </p>
                            <div style="display:flex; gap:10px; margin-top:10px;">
                                <input type="text" id="video-whitelist-domain" placeholder="Domain (e.g., example.com)" style="flex:1; padding:8px;">
                                <button id="add-video-whitelist" style="padding:8px 16px; background:#4285f4; color:white; border:none; border-radius:4px; cursor:pointer;">Add</button>
                            </div>
                            <div id="video-whitelist" style="margin-top:10px; max-height:150px; overflow-y:auto;"></div>
                        </div>
                    </div>

                    <!-- Download Blocking -->
                    <div style="margin:20px 0; padding-top:10px; border-top:1px solid #eee;">
                        <h3>Download Blocking</h3>
                        <div style="display:flex; align-items:center; margin-bottom:10px;">
                            <label style="display:flex; align-items:center; cursor:pointer;">
                                <input type="checkbox" id="block-downloads" style="margin-right:10px;">
                                <span>Block all downloads</span>
                            </label>
                        </div>
                        <div style="margin-top:15px;">
                            <h4>Download Type Whitelist</h4>
                            <p style="color:#666; font-size:12px; margin-bottom:10px;">
                                Add file extensions that should be allowed for download (e.g., pdf, docx)
                            </p>
                            <div style="display:flex; gap:10px; margin-top:10px;">
                                <input type="text" id="download-whitelist-ext" placeholder="File extension (e.g., pdf)" style="flex:1; padding:8px;">
                                <button id="add-download-whitelist" style="padding:8px 16px; background:#4285f4; color:white; border:none; border-radius:4px; cursor:pointer;">Add</button>
                            </div>
                            <div id="download-whitelist" style="margin-top:10px; max-height:150px; overflow-y:auto;"></div>
                        </div>
                    </div>

                    <div style="margin:20px 0; text-align:right;">
                        <button id="save-parental-control" style="padding:10px 20px; background:#4285f4; color:white; border:none; border-radius:4px; cursor:pointer;">Save Changes</button>
                    </div>
                </div>

                <!-- General Settings Panel -->
                <div class="settings-panel" id="panel-general" style="display:none;">
                    <h2>General Settings</h2>
                    <div style="margin:20px 0;">
                        <h3>Display Options</h3>
                        <div style="background:#f5f5f5; padding:15px; border-radius:8px; margin-bottom:20px;">
                            <h4 style="margin-top:0; margin-bottom:15px; color:#333;">Theme Settings</h4>
                            <label style="display:block; margin-bottom:15px;">
                                <input type="checkbox" id="setting-dark-mode">
                                <span style="margin-left:5px; font-weight:500;">Enable Dark Mode</span>
                            </label>
                            <p style="margin:0; font-size:12px; color:#666;">
                                Dark mode applies to both the browser interface and web content.
                            </p>
                        </div>

                        <label style="display:block; margin-bottom:15px;">
                            <input type="checkbox" id="setting-show-bookmarks-toolbar">
                            <span style="margin-left:5px;">Show bookmarks in toolbar</span> <span style="font-size:11px; color:#666;">(requires restart)</span>
                        </label>
                        <label style="display:block; margin-bottom:15px;">
                            <input type="checkbox" id="setting-start-fullscreen">
                            <span style="margin-left:5px;">Start in fullscreen mode</span>
                        </label>
                        <label style="display:block; margin-bottom:15px;">
                            <input type="checkbox" id="setting-show-timer" checked>
                            <span style="margin-left:5px;">Show timer in top-left corner</span>
                        </label>
                        <label style="display:block; margin-bottom:15px;">
                            <input type="checkbox" id="setting-show-remaining-time" checked>
                            <span style="margin-left:5px;">Show remaining time instead of elapsed time</span>
                        </label>
                    </div>

                    <div style="margin:20px 0; text-align:right;">
                        <button id="save-general-settings" style="padding:10px 20px; background:#4285f4; color:white; border:none; border-radius:4px; cursor:pointer;">Save Changes</button>
                    </div>
                </div>

                <!-- Websites & Cookies Panel -->
                <div class="settings-panel" id="panel-websites-cookies" style="display:none;">
                    <h2>Websites & Cookies</h2>
                    <div style="margin:20px 0;">
                        <h3>Cookie Management</h3>
                        <label style="display:block; margin-bottom:15px;">
                            <input type="checkbox" id="setting-save-cookies">
                            <span style="margin-left:5px; font-weight:500;">Preserve cookies and site data when closing the browser</span>
                        </label>
                        <p style="margin:0; font-size:12px; color:#666; margin-bottom:15px;">
                            If enabled, cookies and site data will be saved between sessions. If disabled, all cookies and site data will be cleared when you close the browser.
                        </p>

                        <div style="margin-top:20px;">
                            <h4>Cookie Whitelist</h4>
                            <p style="color:#666; font-size:12px; margin-bottom:10px;">
                                Add domains where cookies should be preserved even when the above option is disabled (e.g., google.com)
                            </p>
                            <div style="display:flex; gap:10px; margin-top:10px;">
                                <input type="text" id="cookie-whitelist-domain" placeholder="Domain (e.g., example.com)" style="flex:1; padding:8px;">
                                <button id="add-cookie-whitelist" style="padding:8px 16px; background:#4285f4; color:white; border:none; border-radius:4px; cursor:pointer;">Add</button>
                            </div>
                            <div id="cookie-whitelist" style="margin-top:10px; max-height:200px; overflow-y:auto;"></div>
                        </div>
                    </div>

                    <div style="margin:20px 0; text-align:right;">
                        <button id="save-websites-cookies" style="padding:10px 20px; background:#4285f4; color:white; border:none; border-radius:4px; cursor:pointer;">Save Changes</button>
                    </div>
                </div>

                <!-- Advanced Settings Panel -->
                <div class="settings-panel" id="panel-advanced" style="display:none;">
                    <h2>Advanced Settings</h2>
                    <div style="margin:20px 0;">
                        <h3>Block Domains</h3>
                        <div style="display:flex; gap:10px; margin-bottom:8px;">
                            <input type="text" id="blocklist-domains-input" placeholder="e.g. facebook.com" style="flex:1; padding:12px 14px; border-radius:8px; border:1.5px solid #c0c0c0; font-size:1em; background:#f7f8fa;">
                            <button id="add-blocklist-domain" style="padding:8px 16px; background:#4285f4; color:white; border:none; border-radius:4px; cursor:pointer;">Add</button>
                        </div>
                        <div id="blocklist-domains-list" style="margin-bottom:8px;"></div>
                        <p style="margin:0; font-size:12px; color:#666;">One domain per line. Example: <code>facebook.com</code></p>
                    </div>
                    <div style="margin:20px 0;">
                        <h3>Block URLs</h3>
                        <div style="display:flex; gap:10px; margin-bottom:8px;">
                            <input type="text" id="blocklist-urls-input" placeholder="e.g. https://example.com/bad or *.adultsite.com or /.*ads.*/" style="flex:1; padding:12px 14px; border-radius:8px; border:1.5px solid #c0c0c0; font-size:1em; background:#f7f8fa;">
                            <button id="add-blocklist-url" style="padding:8px 16px; background:#4285f4; color:white; border:none; border-radius:4px; cursor:pointer;">Add</button>
                        </div>
                        <div id="blocklist-urls-list" style="margin-bottom:8px;"></div>
                        <p style="margin:0; font-size:12px; color:#666;">One URL, wildcard, or regex per line. Example: <code>https://example.com/bad</code>, <code>*.adultsite.com</code>, <code>/.*ads.*/</code></p>
                    </div>
                    <div style="margin:20px 0;">
                        <h3>Block Keywords</h3>
                        <div style="display:flex; gap:10px; margin-bottom:8px;">
                            <input type="text" id="blocklist-keywords-input" placeholder="e.g. badword" style="flex:1; padding:12px 14px; border-radius:8px; border:1.5px solid #c0c0c0; font-size:1em; background:#f7f8fa;">
                            <button id="add-blocklist-keyword" style="padding:8px 16px; background:#4285f4; color:white; border:none; border-radius:4px; cursor:pointer;">Add</button>
                        </div>
                        <div id="blocklist-keywords-list" style="margin-bottom:8px;"></div>
                        <p style="margin:0; font-size:12px; color:#666;">One keyword per line. Example: <code>badword</code></p>
                    </div>
                    <div style="margin:20px 0;">
                        <h3>Redirect Blocked Requests</h3>
                        <input type="text" id="redirect-url" placeholder="Enter redirect URL (e.g., https://safe.com)" style="width:100%; border-radius:8px; border:1.5px solid #c0c0c0; padding:12px 14px; font-size:1em; margin-bottom:8px; background:#f7f8fa;">
                        <p style="margin:0; font-size:12px; color:#666;">
                            If set, users will be redirected to this URL when a block rule matches.<br>
                            Leave blank to show a block page instead.
                        </p>
                    </div>
                    <div style="margin:20px 0; text-align:right;">
                        <button id="save-advanced-settings" style="padding:10px 20px; background:#4285f4; color:white; border:none; border-radius:4px; cursor:pointer;">Save Changes</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Password Prompt Modal (Completely rebuilt) -->
    <div id="password-modal" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.7); z-index:1001;">
        <div style="position:absolute; top:50%; left:50%; transform:translate(-50%, -50%); background:#fff; padding:20px; border-radius:8px; width:350px; box-shadow:0 0 20px rgba(0,0,0,0.5);">
            <h3 style="margin-top:0; color:#333;">Password Required</h3>
            <p style="color:#555;" id="password-prompt-message">Please enter your password to access time limit settings:</p>

            <form id="password-form" onsubmit="return false;">
                <input type="password" id="password-input" style="width:100%; padding:10px; margin:15px 0; border:1px solid #ccc; border-radius:4px; font-size:16px; box-sizing:border-box;" placeholder="Enter password" autocomplete="off">

                <div style="display:flex; justify-content:flex-end; gap:10px; margin-top:20px;">
                    <button type="button" id="cancel-password" style="padding:10px 16px; border:1px solid #ccc; background:#f5f5f5; border-radius:4px; cursor:pointer; font-size:14px;">Cancel</button>
                    <button type="submit" id="submit-password" style="padding:10px 16px; background:#4285f4; color:white; border:none; border-radius:4px; cursor:pointer; font-size:14px;">Submit</button>
                </div>
            </form>

            <p id="password-error" style="color:#d32f2f; margin-top:10px; display:none; font-weight:bold;">Incorrect password. Please try again.</p>
        </div>
    </div>



    <script>
        // Add global error handlers to catch GUEST_VIEW_MANAGER_CALL errors at renderer level
        window.addEventListener('error', function(event) {
            if (event.error && event.error.message && event.error.message.includes('GUEST_VIEW_MANAGER_CALL')) {
                console.log('Caught GUEST_VIEW_MANAGER_CALL error in global handler, suppressing:', event.error.message);
                event.preventDefault();
                return false;
            }

            if (event.error && event.error.message && event.error.message.includes('ERR_ABORTED')) {
                console.log('Caught ERR_ABORTED error in global handler, suppressing:', event.error.message);
                event.preventDefault();
                return false;
            }
        });

        window.addEventListener('unhandledrejection', function(event) {
            if (event.reason && event.reason.toString().includes('GUEST_VIEW_MANAGER_CALL')) {
                console.log('Caught GUEST_VIEW_MANAGER_CALL promise rejection, suppressing:', event.reason);
                event.preventDefault();
                return false;
            }

            if (event.reason && event.reason.toString().includes('ERR_ABORTED')) {
                console.log('Caught ERR_ABORTED promise rejection, suppressing:', event.reason);
                event.preventDefault();
                return false;
            }
        });

        const urlInput = document.getElementById('url-input');
        const backButton = document.getElementById('back');
        const forwardButton = document.getElementById('forward');
        const reloadButton = document.getElementById('reload');
        const goButton = document.getElementById('go');
        const newTabButton = document.getElementById('new-tab-btn');
        const addBookmarkButton = document.getElementById('add-bookmark');
        const tabsContainer = document.getElementById('tabs');
        const bookmarksContainer = document.getElementById('bookmarks');
        const webviewContainer = document.getElementById('webview-container');
        const settingsButton = document.getElementById('settings');
        const settingsModal = document.getElementById('settings-modal');
        const closeSettingsBtn = document.getElementById('close-settings');
        const settingsTabs = document.querySelectorAll('.settings-tab');
        const settingsPanels = document.querySelectorAll('.settings-panel');
        const bookmarksList = document.getElementById('bookmarks-list');
        const newBookmarkTitle = document.getElementById('new-bookmark-title');
        const newBookmarkUrl = document.getElementById('new-bookmark-url');
        const addNewBookmarkBtn = document.getElementById('add-new-bookmark');

        // Time limit elements
        const timerDisplay = document.getElementById('timer-display');
        const timerText = document.getElementById('timer-text');
        const limitYoutube = document.getElementById('limit-youtube');
        const limitFacebook = document.getElementById('limit-facebook');
        const limitTotal = document.getElementById('limit-total');
        const resetYoutube = document.getElementById('reset-youtube');
        const resetFacebook = document.getElementById('reset-facebook');
        const resetTotal = document.getElementById('reset-total');
        const customDomain = document.getElementById('custom-domain');
        const customMinutes = document.getElementById('custom-minutes');
        const addCustomLimit = document.getElementById('add-custom-limit');
        const customLimitsList = document.getElementById('custom-limits-list');
        const saveTimeLimits = document.getElementById('save-time-limits');
        const showTimer = document.getElementById('setting-show-timer');
        const showRemainingTime = document.getElementById('setting-show-remaining-time');

        // Dark mode elements
        const darkModeToggle = document.getElementById('setting-dark-mode');
        const saveGeneralSettings = document.getElementById('save-general-settings');

        // Password protection elements
        const passwordModal = document.getElementById('password-modal');
        const passwordInput = document.getElementById('password-input');
        const passwordPromptMessage = document.getElementById('password-prompt-message');
        const submitPassword = document.getElementById('submit-password');
        const cancelPassword = document.getElementById('cancel-password');
        const passwordError = document.getElementById('password-error');
        const settingPassword = document.getElementById('setting-password');
        const savePassword = document.getElementById('save-password-new');
        const deletePasswordBtn = document.getElementById('delete-password');
        // Add reference for confirm password
        let confirmSettingPassword = document.getElementById('confirm-setting-password');
        if (!confirmSettingPassword) {
            // Create and insert the confirm password field if it doesn't exist
            confirmSettingPassword = document.createElement('input');
            confirmSettingPassword.type = 'password';
            confirmSettingPassword.id = 'confirm-setting-password';
            confirmSettingPassword.placeholder = 'Confirm password';
            confirmSettingPassword.style.flex = '1';
            confirmSettingPassword.style.padding = '10px';
            confirmSettingPassword.style.border = '1px solid #ccc';
            confirmSettingPassword.style.borderRadius = '4px';
            confirmSettingPassword.style.fontSize = '14px';
            // Insert after settingPassword
            const pwdRow = settingPassword.parentNode;
            pwdRow.insertBefore(confirmSettingPassword, pwdRow.children[1]);
        }

        // Debug check for elements
        console.log('Delete password button found:', !!deletePasswordBtn);

        // Tab management
        let tabs = [];
        let activeTabId = null;

        // Create a new tab
        function createTab(url = 'https://www.google.com') {
            console.log('Creating new tab with URL:', url);

            try {
                // Create a more robust unique tab ID
                const tabId = 'tab-' + Date.now() + '-' + Math.floor(Math.random() * 1000000);
                console.log('Generated tab ID:', tabId);

                const tab = document.createElement('div');
                tab.className = 'tab';
                tab.dataset.id = tabId;
                tab.textContent = 'Tab';

                // Create close button
                const closeBtn = document.createElement('span');
                closeBtn.textContent = '×';
                closeBtn.style.marginLeft = '8px';
                closeBtn.style.cursor = 'pointer';
                closeBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    closeTab(tabId);
                });
                tab.appendChild(closeBtn);

                console.log('Tab element created');

                // Create webview
                console.log('Creating webview element');
                const webview = document.createElement('webview');
                webview.id = tabId;

                // Set webview preferences
                webview.setAttribute('preload', './preload.js');

                // Enable downloads in webview
                // webview.setAttribute('allowpopups', 'true'); // Removed due to security warning

                // DIRECT APPROACH: Block images at webview creation time
                console.log('CREATE WEBVIEW: Creating webview for URL:', url);

                // Check if image blocking is enabled
                const blockImagesEnabled = localStorage.getItem('blockImages') === 'true';
                const imageWhitelistItems = JSON.parse(localStorage.getItem('imageWhitelist') || '[]');

                // Extract domain from URL to check whitelist
                let domain = '';
                try {
                    const urlObj = new URL(url);
                    domain = urlObj.hostname;
                } catch (e) {
                    console.error('Error extracting domain from URL:', e);
                }

                const isWhitelisted = isDomainWhitelisted_NEW(domain, imageWhitelistItems);
                console.log('CREATE TAB WHITELIST CHECK: For domain:', domain, 'isWhitelisted result:', isWhitelisted, 'using whitelist:', JSON.stringify(imageWhitelistItems));
                console.log('CREATE WEBVIEW: Domain:', domain, 'Image blocking enabled:', blockImagesEnabled, 'Is whitelisted:', isWhitelisted);

                // Always use the default partition. Image blocking will be handled dynamically.
                console.log('CREATE WEBVIEW: Setting partition to persist:default for:', domain);
                webview.setAttribute('partition', 'persist:default');

                // Do NOT set webpreferences="images=false" here.
                // We will rely on dynamic blocking via webRequest and CSS.
                // If webview.hasAttribute('webpreferences') { // Clear if somehow set
                // webview.removeAttribute('webpreferences');
                // console.log('CREATE WEBVIEW: Ensured webpreferences is not set for images=false initially.');
                // }

                // Initialize tracking properties with improved whitelist logic
                webview.imagesDisabled = blockImagesEnabled && !isWhitelisted; // What should happen
                webview.wasImagesDisabled = webview.imagesDisabled; // Store the initial intended state
                webview.needsDynamicImageUnblockReload = true; // This flag is used by dom-ready to know if it transitioned from a blocked state.
                webview.currentLoadedUrl = url; // Store the initial URL
                webview.isReloadingForImageUnblock = false;
                webview.reloadTargetUrlForImageUnblock = null;

                if (blockImagesEnabled && !isWhitelisted) {
                    console.log('CREATE WEBVIEW: Dynamic image blocking will be applied for domain:', domain, '(improved whitelist logic)');
                    webview.classList.add('image-blocking-enabled');
                } else {
                    console.log('CREATE WEBVIEW: Images will be allowed for domain:', domain, '(whitelisted or blocking disabled)');
                }

                // Set src directly to ensure initial loading works
                webview.src = url;
                webview.style.display = 'none'; // Hide initially
                webview.style.width = '100%';
                webview.style.height = '100%';
                webview.style.flex = '1';

                console.log('Webview element created');

                // Add event listeners
                webview.addEventListener('did-start-navigation', (event) => {
                    console.log('DID-START-NAVIGATION: Checking with improved whitelist logic for:', event.url);

                    // Check image blocking settings with improved whitelist logic
                    const globalBlockImages = localStorage.getItem('blockImages') === 'true';
                    if (globalBlockImages && event.url !== 'about:blank' && !event.isSameDocument) {
                        // Extract domain to check whitelist
                        const navigationDomain = extractDomain(event.url);
                        const imageWhitelist = JSON.parse(localStorage.getItem('imageWhitelist') || '[]');

                        const isWhitelisted = isDomainWhitelisted_NEW(navigationDomain, imageWhitelist);

                        if (!isWhitelisted) {
                            console.log('DID-START-NAVIGATION: Preload script will query main process to block images for:', navigationDomain);
                        } else {
                            console.log('DID-START-NAVIGATION: Preload script will query main process, should not block images for whitelisted domain:', navigationDomain);
                        }
                    } else if (event.url !== 'about:blank' && !event.isSameDocument) {
                        console.log('DID-START-NAVIGATION: Global image blocking is OFF. Preload will query main process.');
                    }

                    if (!event.isSameDocument) {
                        const eventUrl = event.url;
                        const previousUrlInState = webview.currentLoadedUrl || '';

                        console.log(`D-S-N DEBUG: EventURL: "${eventUrl}", PrevURLInState: "${previousUrlInState}", isReloadingSpecialFlag: ${!!webview.isReloadingForImageUnblock}, reloadTargetURL: "${webview.reloadTargetUrlForImageUnblock}", currentNeedsReloadFlag: ${webview.needsDynamicImageUnblockReload}`);

                        if (webview.isReloadingForImageUnblock && eventUrl === webview.reloadTargetUrlForImageUnblock) {
                            console.log(`D-S-N (Image Unblock Reload): EventURL "${eventUrl}" matches target. Flag "needsDynamicImageUnblockReload" (${webview.needsDynamicImageUnblockReload}) remains unchanged (should be false).`);
                            // This is the specific reload we triggered for image unblocking.
                            // needsDynamicImageUnblockReload should already be false.
                            webview.currentLoadedUrl = eventUrl;
                            webview.isReloadingForImageUnblock = false; // Consume the flag
                            webview.reloadTargetUrlForImageUnblock = null;
                        } else if (eventUrl !== previousUrlInState && eventUrl !== 'about:blank') {
                            console.log(`D-S-N (New Page): EventURL "${eventUrl}" from "${previousUrlInState}". Setting "needsDynamicImageUnblockReload" to true.`);
                            webview.needsDynamicImageUnblockReload = true;
                            webview.currentLoadedUrl = eventUrl;
                            webview.isReloadingForImageUnblock = false; // Ensure special flag is clear for normal new navigations
                            webview.reloadTargetUrlForImageUnblock = null;
                        } else if (eventUrl === 'about:blank') {
                            console.log(`D-S-N (about:blank): EventURL "${eventUrl}". "needsDynamicImageUnblockReload" (${webview.needsDynamicImageUnblockReload}) and "currentLoadedUrl" (${previousUrlInState}) unchanged. Special reload flag "isReloadingForImageUnblock" (${!!webview.isReloadingForImageUnblock}) also unchanged unless consumed by target match.`);
                        } else { // eventUrl === previousUrlInState (and not the specific image unblock reload)
                            console.log(`D-S-N (Same Page Reload / Other): EventURL "${eventUrl}". "needsDynamicImageUnblockReload" (${webview.needsDynamicImageUnblockReload}) unchanged. Clearing special reload flag if set.`);
                            webview.isReloadingForImageUnblock = false; // Ensure special flag is clear
                            webview.reloadTargetUrlForImageUnblock = null;
                        }
                    } else {
                        console.log(`D-S-N (Same Document): Skipped for "${event.url}". "needsDynamicImageUnblockReload" (${webview.needsDynamicImageUnblockReload}) unchanged.`);
                    }
                });

                webview.addEventListener('did-navigate', (event) => {
                    console.log('Navigation occurred:', event.url);
                    console.log('DID-NAVIGATE: Using improved whitelist logic for image blocking decisions');

                    if (tabId === activeTabId) {
                        urlInput.value = event.url;
                    }
                });

                webview.addEventListener('page-title-updated', (event) => {
                    console.log('Page title updated:', event.title);
                    // Truncate title to keep tabs small
                    let shortTitle = event.title;
                    if (shortTitle.length > 15) {
                        shortTitle = shortTitle.substring(0, 15) + '...';
                    }
                    tab.textContent = shortTitle;
                    // Re-add the close button as it gets overwritten
                    tab.appendChild(closeBtn);
                });

                webview.addEventListener('did-fail-load', (event) => {
                    console.error(`Failed to load: URL: ${event.validatedURL}, ErrorCode: ${event.errorCode}, Description: ${event.errorDescription}, IsMainFrame: ${event.isMainFrame}`);

                    // Error codes: https://source.chromium.org/chromium/chromium/src/+/main:net/base/net_error_list.h
                    if (event.errorCode === -3) { // ERR_ABORTED
                        console.warn('Load aborted (ERR_ABORTED) for URL:', event.validatedURL, 'MainFrame:', event.isMainFrame);

                        // COMPLETELY DISABLE ALL ERR_ABORTED RETRIES
                        // ERR_ABORTED is causing GUEST_VIEW_MANAGER_CALL errors when we try to retry
                        // Most ERR_ABORTED cases are intentional navigation cancellations anyway

                        console.log('ERR_ABORTED detected - treating as intentional navigation cancellation, no retry will be attempted');
                        console.log('This prevents GUEST_VIEW_MANAGER_CALL errors from conflicting navigation attempts');
                        return;
                    }

                    if (event.errorCode === -2) { // ERR_FAILED
                        console.warn('Load failed (ERR_FAILED) for URL:', event.validatedURL);

                        // Initialize retry counter if not exists
                        if (!webview._retryCount) {
                            webview._retryCount = {};
                        }

                        const retryKey = event.validatedURL || 'unknown';
                        const currentRetries = webview._retryCount[retryKey] || 0;

                        // Limit retries to prevent infinite loops
                        if (currentRetries >= 1) {
                            console.log(`ERR_FAILED retry limit reached for ${retryKey}, not retrying`);
                            return;
                        }

                        // Extract domain to check if it's whitelisted
                        const domain = extractDomain(event.validatedURL);
                        const imageWhitelist = JSON.parse(localStorage.getItem('imageWhitelist') || '[]');
                        const isWhitelisted = isDomainWhitelisted_NEW(domain, imageWhitelist);

                        console.log(`ERR_FAILED analysis: Domain: ${domain}, Whitelisted: ${isWhitelisted}, Retries: ${currentRetries}`);

                        // Check if this might be due to our image blocking or other restrictions
                        const hasImagesBlockedAtCreation = webview.getAttribute('webpreferences') === 'images=false';
                        const partition = webview.getAttribute('partition');

                        console.log(`Webview config: images blocked: ${hasImagesBlockedAtCreation}, partition: ${partition}`);

                        // For whitelisted sites, try a more permissive approach
                        if (isWhitelisted && hasImagesBlockedAtCreation) {
                            console.log('ERR_FAILED on whitelisted site with image blocking. Attempting recovery with images enabled...');

                            // Increment retry counter
                            webview._retryCount[retryKey] = currentRetries + 1;

                            // Create a new webview with images enabled for this whitelisted site
                            setTimeout(() => {
                                try {
                                    if (webview && document.body.contains(webview) && !webview.isLoading()) {
                                        // Update webview preferences to allow images
                                        webview.setAttribute('webpreferences', 'images=true, javascript=true, webSecurity=true, contextIsolation=true');
                                        webview.setAttribute('partition', 'persist:main');

                                        console.log(`ERR_FAILED retry disabled to prevent GUEST_VIEW_MANAGER_CALL errors`);
                                        // webview.loadURL(event.validatedURL); // DISABLED
                                    }
                                } catch (retryErr) {
                                    console.error('Error during ERR_FAILED recovery attempt:', retryErr);
                                }
                            }, 1000);
                            return;
                        }

                        // For non-whitelisted sites or other cases, don't retry automatically
                        // as ERR_FAILED often indicates a permanent failure
                        console.log('ERR_FAILED on non-whitelisted site or other issue. Not retrying automatically.');
                    }

                    if (event.isMainFrame) {
                        // For other errors in the main frame, show a custom error page.
                        webview.loadURL('about:blank'); // Load a blank page first
                    webview.executeJavaScript(`
                            document.body.style.fontFamily = "Arial, sans-serif";
                            document.body.style.margin = "0";
                            document.body.style.padding = "20px";
                            document.body.style.textAlign = "center";
                            document.body.innerHTML = \`
                                <h2>Page Load Error</h2>
                                <p>Could not load: ${event.validatedURL ? event.validatedURL.replace(/</g, "&lt;").replace(/>/g, "&gt;") : 'the requested page'}</p>
                                <p>Error: ${event.errorDescription ? event.errorDescription.replace(/</g, "&lt;").replace(/>/g, "&gt;") : 'Unknown error'} (${event.errorCode})</p>
                                <button onclick="window.location.reload()">Try Again</button>
                            \`;
                        `).catch(execJsError => console.error('Error executing JS for error page:', execJsError));
                    } else {
                        // For subframe errors, just log them. Don't disrupt the main page.
                        console.warn('Subframe failed to load:', event.validatedURL, 'Error:', event.errorDescription, `(${event.errorCode})`);
                    }
                });

                // Configure webview when ready
                webview.addEventListener('dom-ready', () => {
                    webview.setZoomFactor(1);

                    // Get current URL and domain once
                    const currentUrl = webview.getURL();
                    const currentDomain = extractDomain(currentUrl);

                    // Clear retry counters on successful navigation
                    if (webview._retryCount) {
                        console.log('Clearing retry counters after successful navigation');
                        webview._retryCount = {};
                    }
                    if (webview._guestViewRetryCount) {
                        webview._guestViewRetryCount = {};
                    }

                    // No special domain handling - all domains must be explicitly whitelisted by the user

                    // Apply dark mode if enabled
                    const darkModeEnabled = localStorage.getItem('darkMode') === 'true';
                    if (darkModeEnabled) {
                        try {
                            // Special handling for Google and YouTube built-in dark mode
                            if (currentDomain.endsWith('google.com')) {
                                // Google Search: set theme=dark in URL if not present, but only reload once per tab
                                if (!/([?&]theme=dark)/.test(currentUrl) && !webview._googleDarkReloaded && !webview.isLoading()) {
                                    let newUrl = currentUrl;
                                    if (currentUrl.includes('?')) {
                                        newUrl += '&theme=dark';
                                    } else {
                                        newUrl += '?theme=dark';
                                    }
                                    webview._googleDarkReloaded = true;
                                    console.log('Google dark mode reload disabled to prevent GUEST_VIEW_MANAGER_CALL errors');
                                    // webview.loadURL(newUrl); // DISABLED
                                    return; // Skip reload
                                }
                            } else if (currentDomain.endsWith('youtube.com')) {
                                // YouTube: set PREF cookie and localStorage for dark mode, only if not loading
                                if (!webview.isLoading()) {
                                    webview.executeJavaScript(`
                                        try {
                                            document.cookie = 'PREF=f6=400; path=/; domain=.youtube.com';
                                            localStorage.setItem('yt-dark-mode', 'true');
                                            document.documentElement.setAttribute('dark', 'true');
                                        } catch (e) { console.error('Failed to set YouTube dark mode:', e); }
                                    `).catch(e => console.error('Error executing YouTube dark mode JS:', e));
                                }
                            } else {
                                // Fallback: inject dark mode CSS for other sites, only if not loading
                                if (!webview.isLoading()) {
                                    webview.insertCSS(`
                                        html, body {
                                            background-color: #101218 !important;
                                            color: #e0e0e0 !important;
                                        }
                                        a {
                                            color: #4285f4 !important;
                                        }
                                        input, textarea, select {
                                            background-color: #333 !important;
                                            color: #e0e0e0 !important;
                                            border-color: #444 !important;
                                        }
                                    `).catch(e => console.error('Error injecting dark mode CSS:', e));
                                }
                            }
                        } catch (e) {
                            console.error('Error applying dark mode to new webview:', e);
                        }
                    }

                    // Video blocking functionality is disabled (settings preserved for UI)

                    // IMPROVED: Image blocking with enhanced whitelist matching for related domains
                    console.log('DOM READY EVENT: Checking image blocking settings');

                    const blockImagesEnabled = localStorage.getItem('blockImages') === 'true';
                    console.log('DOM READY EVENT: Domain:', currentDomain);
                    console.log('DOM READY EVENT: Image blocking enabled:', blockImagesEnabled);

                    // Get fresh whitelist from localStorage for dynamic blocking logic
                    const currentImageWhitelist = JSON.parse(localStorage.getItem('imageWhitelist') || '[]');
                    console.log('DOM READY EVENT: Current Whitelist for dynamic check:', currentImageWhitelist);

                    // Use improved isDomainWhitelisted_NEW for all checks in dom-ready
                    const isDomainCurrentlyWhitelisted = isDomainWhitelisted_NEW(currentDomain, currentImageWhitelist);
                    console.log('DOM READY EVENT: Domain', currentDomain, 'is whitelisted (improved logic):', isDomainCurrentlyWhitelisted);

                    // Get webContents to access settings
                    const remote = require('@electron/remote');
                    const webContentsId = webview.getWebContentsId();

                    if (webContentsId) {
                        const wc = remote.webContents.fromId(webContentsId);
                        if (wc && wc.session) {
                            wc.session.webRequest.onBeforeRequest(null); // Clear any existing request blockers first

                            console.log('DOM-READY: Image Handling with improved whitelist logic for:', currentDomain);

                            if (blockImagesEnabled && !isDomainCurrentlyWhitelisted) {
                                console.log('DOM-READY: BLOCKING IMAGES for', currentDomain, 'as it is NOT whitelisted and global blocking is ON.');

                                // Use improved URL pattern that includes the domain check with enhanced whitelist logic
                                wc.session.webRequest.onBeforeRequest({ urls: ['*://*/*'] }, (details, callback) => {
                                    if (details.resourceType === 'image') {
                                        // Extract domain from the request URL
                                        let requestDomain = '';
                                        try {
                                            const urlObj = new URL(details.url);
                                            requestDomain = urlObj.hostname;
                                        } catch (e) {
                                            console.error('Error parsing URL in webRequest handler:', e);
                                        }

                                        // Check if the request domain is whitelisted using improved logic
                                        const isRequestDomainWhitelisted = isDomainWhitelisted_NEW(requestDomain, currentImageWhitelist);

                                        if (isRequestDomainWhitelisted) {
                                            console.log('ALLOWING image request to whitelisted domain:', requestDomain, details.url);
                                            callback({});
                                        } else {
                                            console.log('BLOCKING image request to non-whitelisted domain:', requestDomain, details.url);
                                            callback({ cancel: true });
                                        }
                                    } else {
                                        callback({});
                                    }
                                });

                                webview.imagesDisabled = true; // Update internal flag
                            } else {
                                console.log('DOM-READY: ENABLING IMAGES for', currentDomain, '(whitelisted or global blocking OFF).');

                                // Ensure images are enabled
                                webview.executeJavaScript(`
                                    (function() {
                                        console.log('IMAGE BLOCKER: Ensuring images are visible for', window.location.hostname);

                                        // Remove any existing image blocker style
                                        const existingBlocker = document.getElementById('electron-browser-image-blocker');
                                        if (existingBlocker) {
                                            existingBlocker.remove();
                                        }

                                        // Restore original src attributes for images if they were stored
                                        const images = document.querySelectorAll('img[data-original-src]');
                                        images.forEach(img => {
                                            const originalSrc = img.getAttribute('data-original-src');
                                            if (originalSrc) {
                                                img.src = originalSrc;
                                            }
                                        });
                                    })();
                                `).catch(e => console.error('Error executing image enabler JS:', e));

                                webview.imagesDisabled = false; // Update internal flag
                            }

                            // VIDEO BLOCKING SETTINGS CHECK (UI ONLY - NO ACTUAL BLOCKING)
                            const blockVideosEnabled = localStorage.getItem('blockVideos') === 'true';
                            const currentVideoWhitelist = JSON.parse(localStorage.getItem('videoWhitelist') || '[]');
                            const isVideoDomainCurrentlyWhitelisted = isDomainWhitelisted_NEW(currentDomain, currentVideoWhitelist);

                            console.log('DOM-READY: Video blocking settings check: enabled:', blockVideosEnabled, 'isWhitelisted:', isVideoDomainCurrentlyWhitelisted, 'Domain:', currentDomain);
                            console.log('DOM-READY: Video blocking functionality is disabled - settings preserved for UI only');

                            // Note: Video blocking implementation has been disabled
                            // Settings are preserved and functional in the UI, but no actual blocking occurs
                            // This allows for future re-implementation while maintaining the settings framework

                            // Image blocking logic completed with improved whitelist matching
                            console.log('DOM-READY: Image blocking logic completed with improved whitelist matching');
                        } else {
                            console.error('Webview webContents.session not available from ID on dom-ready for:', currentUrl, 'webContentsId:', webContentsId, 'wc exists:', !!wc);
                        }
                    } else {
                        console.error('Webview getWebContentsId() returned null/undefined on dom-ready for:', currentUrl);
                    }
                });

                // Add to DOM
                tabsContainer.appendChild(tab);
                webviewContainer.appendChild(webview);
                console.log('Added tab and webview to DOM');

                // Store tab info
                tabs.push({
                    id: tabId,
                    tab: tab,
                    webview: webview
                });

                // Set as active if it's the first tab
                if (!activeTabId) {
                    setActiveTab(tabId);
                }

                // Add click handler to activate tab
                tab.addEventListener('click', () => {
                    setActiveTab(tabId);
                });

            console.log('Tab creation complete');
            return tabId;
        } catch (e) {
            console.error('Error in createTab:', e);
            return null;
        }
        }

        // Set active tab
        function setActiveTab(tabId) {
            // Deactivate current tab
            if (activeTabId) {
                const oldTab = tabs.find(t => t.id === activeTabId);
                if (oldTab) {
                    oldTab.tab.classList.remove('active');
                    oldTab.webview.style.display = 'none';
                }
            }

            // Activate new tab
            const newTab = tabs.find(t => t.id === tabId);
            if (newTab && newTab.tab && newTab.webview) {
                newTab.tab.classList.add('active');
                newTab.webview.style.display = 'flex';
                newTab.webview.style.flex = '1';
                activeTabId = tabId;

                // Update URL input
                if (newTab.webview.src) {
                    urlInput.value = newTab.webview.src;
                }
            } else {
                console.warn('Tried to activate a tab that does not exist or is not fully initialized:', tabId);
            }
        }

        // Close tab
        function closeTab(tabId) {
            const index = tabs.findIndex(t => t.id === tabId);
            if (index !== -1) {
                // Remove from DOM
                tabs[index].tab.remove();
                tabs[index].webview.remove();

                // Remove from array
                tabs.splice(index, 1);

                // If we closed the active tab, activate another one
                if (tabId === activeTabId && tabs.length > 0) {
                    setActiveTab(tabs[tabs.length - 1].id);
                } else if (tabs.length === 0) {
                    // If no tabs left, create a new one
                    createTab();
                }
            }
        }

        // Get active webview
        function getActiveWebview() {
            const tab = tabs.find(t => t.id === activeTabId);
            return tab ? tab.webview : null;
        }

        // Navigation
        backButton.addEventListener('click', () => {
            const webview = getActiveWebview();
            if (webview && webview.canGoBack()) {
                webview.goBack();
            }
        });

        forwardButton.addEventListener('click', () => {
            const webview = getActiveWebview();
            if (webview && webview.canGoForward()) {
                webview.goForward();
            }
        });

        reloadButton.addEventListener('click', () => {
            const webview = getActiveWebview();
            if (webview) {
                webview.reload();
            }
        });

        goButton.addEventListener('click', () => {
            const webview = getActiveWebview();
            if (webview) {
                let url = urlInput.value.trim();

                // Check if the input is a URL or a search term
                if (url.includes('.') && !url.includes(' ')) {
                    // Looks like a URL - add protocol if missing
                    if (!url.startsWith('http://') && !url.startsWith('https://')) {
                        url = 'https://' + url;
                    }

                    try {
                        // Validate URL format
                        new URL(url);

                        console.log('Loading URL:', url);
                        webview.loadURL(url).catch(err => {
                            console.error(`Error loading URL '${url}':`, err);

                            // Check if the error is ERR_ABORTED more robustly
                            const isAborted = (err.code === 'ERR_ABORTED' || err.errno === -3 || (err.message && err.message.includes('ERR_ABORTED')));
                            const isGuestViewError = (err.message && err.message.includes('GUEST_VIEW_MANAGER_CALL'));

                            if (isAborted || isGuestViewError) {
                                console.warn(`Loading of ${url} was aborted or had webview issues. Error:`, err.message);

                                // For GUEST_VIEW_MANAGER_CALL errors, don't retry to prevent further conflicts
                                if (isGuestViewError) {
                                    console.log('GUEST_VIEW_MANAGER_CALL error detected - no retry to prevent further conflicts');
                                    console.log('Falling back to Google search immediately');

                                    console.log('GUEST_VIEW_MANAGER_CALL fallback disabled to prevent further conflicts');
                                    // const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(urlInput.value)}`;
                                    // webview.loadURL(searchUrl).catch(searchErr => {
                                    //     console.error(`Failed to load Google search fallback:`, searchErr);
                                    // }); // DISABLED
                                } else {
                                    console.log('ERR_ABORTED detected, treating as intentional - no retry');
                                    // No retry for ERR_ABORTED to prevent GUEST_VIEW_MANAGER_CALL errors
                                }
                            } else {
                                // For other errors, don't attempt fallback to prevent navigation conflicts
                                console.log(`URL load failed for ${url} with a non-abort error. Fallback disabled to prevent GUEST_VIEW_MANAGER_CALL errors`);
                                // const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(urlInput.value)}`;
                                // webview.loadURL(searchUrl).catch(searchErr => {
                                //     console.error(`Failed to load Google search fallback for '${urlInput.value}':`, searchErr);
                                //     // Potentially show an error page here if even search fails
                                // }); // DISABLED
                            }
                        });
                    } catch (e) {
                        console.error('Invalid URL format:', e);
                        // If URL is invalid, don't attempt search to prevent navigation conflicts
                        console.log('Invalid URL format, search fallback disabled to prevent GUEST_VIEW_MANAGER_CALL errors');
                        // const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(urlInput.value)}`;
                        // webview.loadURL(searchUrl); // DISABLED
                    }
                } else {
                    // Input doesn't look like a URL, don't attempt search to prevent navigation conflicts
                    console.log('Search disabled to prevent GUEST_VIEW_MANAGER_CALL errors for:', url);
                    // const searchUrl = `https://www.google.com/search?q=${encodeURIComponent(urlInput.value)}`;
                    // webview.loadURL(searchUrl); // DISABLED
                }
            }
        });

        urlInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                goButton.click();
            }
        });

        // New tab with debounce to prevent rapid multiple tab creation
        const newTabToolbarButton = document.getElementById('new-tab-btn');
        let newTabDebounce = false;
        if (newTabToolbarButton) {
            newTabToolbarButton.addEventListener('click', () => {
                if (newTabDebounce) return;
                newTabDebounce = true;
                createTab();
                setTimeout(() => { newTabDebounce = false; }, 200);
            });
        }

        // Save bookmarks to localStorage
        function saveBookmarks() {
            const bookmarks = [];
            // Get bookmarks from the settings panel
            document.querySelectorAll('.bookmark-item').forEach(bookmark => {
                if (bookmark.dataset.url) {
                    bookmarks.push({
                        title: bookmark.dataset.title,
                        url: bookmark.dataset.url,
                        favicon: bookmark.dataset.favicon || ''
                    });
                }
            });
            localStorage.setItem('bookmarks', JSON.stringify(bookmarks));
        }

        // Load bookmarks from localStorage
        function loadBookmarks() {
            try {
                const bookmarks = JSON.parse(localStorage.getItem('bookmarks')) || [];
                // Clear existing bookmarks
                bookmarksList.innerHTML = '';

                if (bookmarks.length === 0) {
                    bookmarksList.innerHTML = '<div style="padding:10px; color:#666; text-align:center;">No bookmarks yet. Add some using the form below.</div>';
                } else {
                    // Add each bookmark to the list
                    bookmarks.forEach(bookmark => {
                        addBookmarkToList(bookmark.title, bookmark.url, bookmark.favicon);
                    });
                }
            } catch (e) {
                console.error('Failed to load bookmarks:', e);
                bookmarksList.innerHTML = '<div style="padding:10px; color:red;">Error loading bookmarks</div>';
            }
        }

        // Add bookmark to the settings list
        function addBookmarkToList(title, url, faviconUrl = '') {
            const bookmarkItem = document.createElement('div');
            bookmarkItem.className = 'bookmark-item';
            bookmarkItem.dataset.title = title;
            bookmarkItem.dataset.url = url;
            bookmarkItem.dataset.favicon = faviconUrl;

            // Favicon
            const favicon = document.createElement('img');
            favicon.className = 'bookmark-favicon';
            favicon.src = faviconUrl || 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path fill="%23aaa" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/></svg>';
            favicon.onerror = () => {
                favicon.src = 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24"><path fill="%23aaa" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/></svg>';
            };
            bookmarkItem.appendChild(favicon);

            // Title
            const titleElem = document.createElement('div');
            titleElem.className = 'bookmark-title';
            titleElem.textContent = title;
            bookmarkItem.appendChild(titleElem);

            // URL
            const urlElem = document.createElement('div');
            urlElem.className = 'bookmark-url';
            urlElem.textContent = url;
            urlElem.title = url;
            bookmarkItem.appendChild(urlElem);

            // Actions
            const actions = document.createElement('div');
            actions.className = 'bookmark-actions';

            // Open button
            const openBtn = document.createElement('button');
            openBtn.textContent = 'Open';
            openBtn.addEventListener('click', () => {
                const activeWebview = getActiveWebview();
                if (activeWebview) {
                    activeWebview.loadURL(url);
                    settingsModal.style.display = 'none';
                }
            });
            actions.appendChild(openBtn);

            // Delete button
            const deleteBtn = document.createElement('button');
            deleteBtn.textContent = 'Delete';
            deleteBtn.style.color = '#d32f2f';
            deleteBtn.addEventListener('click', () => {
                bookmarkItem.remove();
                saveBookmarks();
            });
            actions.appendChild(deleteBtn);

            bookmarkItem.appendChild(actions);
            bookmarksList.appendChild(bookmarkItem);
        }

        // Add bookmark from current page
        addBookmarkButton.addEventListener('click', () => {
            const webview = getActiveWebview();
            if (webview) {
                // Open settings modal and switch to bookmarks tab
                settingsModal.style.display = 'block';
                settingsTabs.forEach(tab => {
                    tab.classList.toggle('active', tab.dataset.tab === 'bookmarks');
                });
                settingsPanels.forEach(panel => {
                    panel.style.display = panel.id === 'panel-bookmarks' ? 'block' : 'none';
                });

                // Pre-fill the new bookmark form
                const title = webview.getTitle() || 'Untitled';
                const url = webview.getURL();

                newBookmarkTitle.value = title;
                newBookmarkUrl.value = url;

                // Focus on the title field
                newBookmarkTitle.focus();
            }
        });

        // Add new bookmark from the form
        addNewBookmarkBtn.addEventListener('click', () => {
            const title = newBookmarkTitle.value.trim();
            let url = newBookmarkUrl.value.trim();

            if (!title) {
                alert('Please enter a title for the bookmark');
                return;
            }

            if (!url) {
                alert('Please enter a URL for the bookmark');
                return;
            }

            // Add http:// if missing
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                url = 'https://' + url;
            }

            // Try to get favicon
            let faviconUrl = '';
            try {
                const urlObj = new URL(url);
                faviconUrl = `${urlObj.protocol}//${urlObj.hostname}/favicon.ico`;
            } catch (e) {
                console.error('Failed to parse URL for favicon:', e);
            }

            // Check if bookmark already exists
            const existingBookmark = Array.from(document.querySelectorAll('.bookmark-item')).find(
                b => b.dataset.url === url
            );

            if (existingBookmark) {
                // Flash the existing bookmark to indicate it's already there
                existingBookmark.style.backgroundColor = '#ffcc00';
                setTimeout(() => {
                    existingBookmark.style.backgroundColor = '';
                }, 1000);
            } else {
                addBookmarkToList(title, url, faviconUrl);
                saveBookmarks();

                // Clear the form
                newBookmarkTitle.value = '';
                newBookmarkUrl.value = '';
            }
        });

        // Settings modal tab switching
        settingsTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabName = tab.dataset.tab;

                // If trying to access time limits or parental control, check password
                if ((tabName === 'time-limits' || tabName === 'parental-control') && isPasswordProtected()) {
                    console.log('Attempting to access protected tab with password protection:', tabName);
                    showPasswordPrompt(function accessProtectedTab(success) {
                        console.log('Password verification result:', success);
                        if (success) {
                            switchToTab(tabName);

                            // Load parental control settings if needed
                            if (tabName === 'parental-control') {
                                loadParentalControlSettings();
                            }
                            // --- FIX: Reset custom website limits to saved state when opening Time Limits tab ---
                            if (tabName === 'time-limits') {
                                try {
                                    const saved = JSON.parse(localStorage.getItem('timeLimits')) || {};
                                    timeLimits.custom = saved.custom || {};
                                    updateCustomLimitsList();
                                } catch (e) {
                                    console.error('Failed to reset custom limits:', e);
                                }
                            }
                            // --- END FIX ---
                        }
                    });
                } else {
                    console.log('Accessing tab without password:', tabName);
                    switchToTab(tabName);

                    // Load parental control settings if needed
                    if (tabName === 'parental-control' && !isPasswordProtected()) {
                        loadParentalControlSettings();
                    }
                    // --- FIX: Reset custom website limits to saved state when opening Time Limits tab ---
                    if (tabName === 'time-limits') {
                        try {
                            const saved = JSON.parse(localStorage.getItem('timeLimits')) || {};
                            timeLimits.custom = saved.custom || {};
                            updateCustomLimitsList();
                        } catch (e) {
                            console.error('Failed to reset custom limits:', e);
                        }
                    }
                    // --- END FIX ---
                }
            });
        });

        // Open settings modal
        settingsButton.addEventListener('click', () => {
            if (settingsModal) {
                settingsModal.style.display = 'block';
                loadBookmarks(); // Refresh bookmarks list

                // Default to bookmarks tab to avoid password prompt on open
                switchToTab('bookmarks');
            } else {
                console.error('Settings modal not found!');
            }
        });

        // Close settings modal
        closeSettingsBtn.addEventListener('click', () => {
            settingsModal.style.display = 'none';
        });

        // Close modal when clicking outside
        settingsModal.addEventListener('click', (e) => {
            if (e.target === settingsModal) {
                settingsModal.style.display = 'none';
            }
        });

        // Time Limit System
        let timeLimits = {};
        let timeUsage = {};
        let timerInterval = null;
        let currentDomain = '';
        let lastUpdateTime = Date.now();

        // Initialize time limits
        function initTimeLimits() {
            // Load saved time limits or use defaults
            try {
                timeLimits = JSON.parse(localStorage.getItem('timeLimits')) || {
                    youtube: 60,
                    facebook: 25,
                    total: 130,
                    custom: {}
                };

                // Update UI with saved values
                if (limitYoutube) limitYoutube.value = timeLimits.youtube;
                if (limitFacebook) limitFacebook.value = timeLimits.facebook;
                if (limitTotal) limitTotal.value = timeLimits.total;

                // Don't load custom limits here - only when settings panel is opened
                // This prevents password prompt on startup
            } catch (e) {
                console.error('Failed to load time limits:', e);
                timeLimits = {
                    youtube: 60,
                    facebook: 25,
                    total: 130,
                    custom: {}
                };
            }

            // Load time usage or initialize
            try {
                timeUsage = JSON.parse(localStorage.getItem('timeUsage')) || {
                    youtube: 0,
                    facebook: 0,
                    total: 0,
                    custom: {},
                    lastReset: Date.now()
                };

                // Check if we need to reset (new day)
                checkDailyReset();
            } catch (e) {
                console.error('Failed to load time usage:', e);
                timeUsage = {
                    youtube: 0,
                    facebook: 0,
                    total: 0,
                    custom: {},
                    lastReset: Date.now()
                };
            }

            // Load display settings
            const showTimerSetting = localStorage.getItem('showTimer') !== 'false';
            if (showTimer) showTimer.checked = showTimerSetting;
            if (timerDisplay) timerDisplay.style.display = showTimerSetting ? 'flex' : 'none';

            const showRemainingTimeSetting = localStorage.getItem('showRemainingTime') !== 'false';
            if (showRemainingTime) showRemainingTime.checked = showRemainingTimeSetting;

            // Start timer
            startTimer();

            // Update timer display
            updateTimerDisplay();
        }

        // Password protection functions
        function getPassword() {
            // Direct access to localStorage
            return localStorage.getItem('password') || '';
        }

        function setPassword(password) {
            try {
                if (password) {
                    localStorage.setItem('password', password);
                } else {
                    localStorage.removeItem('password');
                }
                return true;
            } catch (e) {
                console.error('Error setting password:', e);
                return false;
            }
        }

        function isPasswordProtected() {
            // Direct check of localStorage for more reliability
            const password = localStorage.getItem('password');
            const hasPassword = password !== null && password !== undefined && password !== '';
            console.log('Password protection check:', hasPassword);
            return hasPassword;
        }

        // Store callback reference
        let currentPasswordCallback = null;

        function showPasswordPrompt(callback) {
            // If no password is set, proceed without prompting
            if (!isPasswordProtected()) {
                callback(true);
                return;
            }

            // Store callback reference
            currentPasswordCallback = callback;

            // Show password modal
            passwordModal.style.display = 'flex';
            passwordInput.value = '';
            passwordError.style.display = 'none';
            passwordInput.focus();
        }

        function verifyPassword(enteredPassword) {
            return enteredPassword === getPassword();
        }

        // Check if we need to reset timers (new day)
        function checkDailyReset() {
            const now = new Date();
            const lastResetDate = new Date(timeUsage.lastReset);

            // Reset if the day has changed (past midnight)
            if (now.getDate() !== lastResetDate.getDate() ||
                now.getMonth() !== lastResetDate.getMonth() ||
                now.getFullYear() !== lastResetDate.getFullYear()) {

                console.log('New day detected, resetting timers');
                resetAllTimers();
            }
        }

        // Reset all timers
        function resetAllTimers() {
            timeUsage = {
                youtube: 0,
                facebook: 0,
                total: 0,
                custom: {},
                lastReset: Date.now()
            };
            saveTimeUsage();
            updateTimerDisplay();
        }

        // Save time usage to localStorage
        function saveTimeUsage() {
            localStorage.setItem('timeUsage', JSON.stringify(timeUsage));
        }

        // Save time limits to localStorage
        function saveTimeLimitsToStorage() {
            localStorage.setItem('timeLimits', JSON.stringify(timeLimits));
        }

        // Start the timer
        function startTimer() {
            if (timerInterval) {
                clearInterval(timerInterval);
            }

            timerInterval = setInterval(() => {
                const now = Date.now();
                const elapsed = (now - lastUpdateTime) / 1000; // seconds
                lastUpdateTime = now;

                // Only count time if actively using
                if (document.visibilityState === 'visible' && currentDomain) {
                    // Update total time
                    timeUsage.total += elapsed / 60; // convert to minutes

                    // Update domain-specific time
                    if (currentDomain.includes('youtube.com')) {
                        timeUsage.youtube += elapsed / 60;
                    } else if (currentDomain.includes('facebook.com')) {
                        timeUsage.facebook += elapsed / 60;
                    }

                    // Update custom domain time
                    for (const domain in timeLimits.custom) {
                        if (currentDomain.includes(domain)) {
                            if (!timeUsage.custom[domain]) {
                                timeUsage.custom[domain] = 0;
                            }
                            timeUsage.custom[domain] += elapsed / 60;
                        }
                    }

                    // Save time usage
                    saveTimeUsage();

                    // Update timer display
                    updateTimerDisplay();

                    // Check time limits
                    checkTimeLimits();
                }

                // Check for daily reset
                checkDailyReset();
            }, 1000);
        }

        // Update the timer display
        function updateTimerDisplay() {
            let usedTime = timeUsage.total;
            let timeLimit = timeLimits.total;
            let domainName = 'Total';

            // Show domain-specific time if on a specific site
            if (currentDomain.includes('youtube.com')) {
                usedTime = timeUsage.youtube;
                timeLimit = timeLimits.youtube;
                domainName = 'YouTube';
            } else if (currentDomain.includes('facebook.com')) {
                usedTime = timeUsage.facebook;
                timeLimit = timeLimits.facebook;
                domainName = 'Facebook';
            } else {
                // Check custom domains
                for (const domain in timeLimits.custom) {
                    if (currentDomain.includes(domain)) {
                        usedTime = timeUsage.custom[domain] || 0;
                        timeLimit = timeLimits.custom[domain];
                        domainName = domain;
                        break;
                    }
                }
            }

            // Calculate time to display (remaining or elapsed)
            let displayTime;
            let isRemaining = showRemainingTime.checked;

            if (isRemaining) {
                // Show remaining time
                displayTime = Math.max(0, timeLimit - usedTime);
            } else {
                // Show elapsed time
                displayTime = usedTime;
            }

            // Format time as MM:SS
            const minutes = Math.floor(displayTime);
            const seconds = Math.floor((displayTime - minutes) * 60);
            const formattedTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

            // Update timer text with prefix
            if (isRemaining) {
                timerText.textContent = `${formattedTime} left`;
            } else {
                timerText.textContent = formattedTime;
            }

            // Set tooltip to show more info
            timerDisplay.title = `${domainName}: ${Math.floor(usedTime)}/${timeLimit} minutes used`;

            // Change color based on time remaining
            const percentUsed = (usedTime / timeLimit) * 100;
            if (percentUsed >= 90) {
                timerDisplay.style.backgroundColor = 'rgba(220, 53, 69, 0.8)'; // Red when close to limit
            } else if (percentUsed >= 75) {
                timerDisplay.style.backgroundColor = 'rgba(255, 193, 7, 0.8)'; // Yellow when getting close
            } else {
                timerDisplay.style.backgroundColor = 'rgba(0, 0, 0, 0.7)'; // Default black
            }
        }

        // Check if time limits have been exceeded
        function checkTimeLimits() {
            let limitExceeded = false;
            let limitMessage = '';

            // Check total time limit
            if (timeUsage.total >= timeLimits.total) {
                limitExceeded = true;
                limitMessage = `You've reached your daily total browsing limit of ${timeLimits.total} minutes.`;
            }

            // Check domain-specific limits
            if (currentDomain.includes('youtube.com') && timeUsage.youtube >= timeLimits.youtube) {
                limitExceeded = true;
                limitMessage = `You've reached your daily YouTube limit of ${timeLimits.youtube} minutes.`;
            } else if (currentDomain.includes('facebook.com') && timeUsage.facebook >= timeLimits.facebook) {
                limitExceeded = true;
                limitMessage = `You've reached your daily Facebook limit of ${timeLimits.facebook} minutes.`;
            }

            // Check custom domain limits
            for (const domain in timeLimits.custom) {
                if (currentDomain.includes(domain) &&
                    timeUsage.custom[domain] &&
                    timeUsage.custom[domain] >= timeLimits.custom[domain]) {
                    limitExceeded = true;
                    limitMessage = `You've reached your daily limit of ${timeLimits.custom[domain]} minutes for ${domain}.`;
                    break;
                }
            }

            // Show limit exceeded message
            if (limitExceeded) {
                const webview = getActiveWebview();
                if (webview) {
                    webview.loadURL('about:blank');
                    webview.executeJavaScript(`
                        document.body.style.margin = '0';
                        document.body.style.padding = '0';
                        document.body.style.fontFamily = 'Arial, sans-serif';
                        document.body.style.background = 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)';

                        document.body.innerHTML = \`
                            <div style="text-align: center; padding: 50px; max-width: 600px; margin: 50px auto; background: white; border-radius: 10px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                                <div style="font-size: 100px; margin-bottom: 20px; animation: pulse 2s infinite;">⏰</div>
                                <h1 style="color: #d32f2f; font-size: 36px; margin-bottom: 20px; text-transform: uppercase;">YOUR TIME IS UP!</h1>
                                <div style="width: 80px; height: 4px; background: #d32f2f; margin: 0 auto 30px;"></div>
                                <p style="font-size: 20px; color: #333; line-height: 1.5; margin-bottom: 20px; font-weight: bold;">${limitMessage}</p>
                                <div style="padding: 20px; background: #f8f8f8; border-radius: 8px; margin: 30px 0; border-left: 4px solid #4285f4;">
                                    <p style="margin: 0; color: #555; font-size: 16px;">All time limits will reset at midnight (00:00).</p>
                                </div>
                                <div style="margin-top: 30px; color: #666;">
                                    <p>You can adjust your time limits in the browser settings.</p>
                                </div>
                            </div>
                            <style>
                                @keyframes pulse {
                                    0% { transform: scale(1); }
                                    50% { transform: scale(1.1); }
                                    100% { transform: scale(1); }
                                }
                            </style>
                        \`;

                        // Add a sound effect
                        const audio = new Audio('data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBTGH0fPTgjMGHm7A7+OZSA0PVqzn77BdGAg+ltryxnMpBSl+zPLaizsIGGS57OihUBELTKXh8bllHgU2jdXzzn0vBSF1xe/glEILElyx6OyrWBUIQ5zd8sFuJAUuhM/z1YU2Bhxqvu7mnEoODlOq5O+zYBoGPJPY88p2KwUme8rx3I4+CRZiturqpVITC0mi4PK8aB8GM4nU8tGAMQYfcsLu45ZFDBFYr+ftrVoXCECY3PLEcSYELIHO8diJOQcZaLvt559NEAxPqOPwtmMcBjiP1/PMeywGI3fH8OCRQQkUXrTp66hVFApGnt/yvmwhBTCG0fPTgjQGHW/A7eSaRw0PVqzl77BeGQc+ltvyxnUoBSh+zPDaizsIGGS56+mjTxELTKXh8bllHgU1jdT0z3wvBSJ0xe/glEILElyx6eyrWRUIRJve8sFwJAQug8/y1oU2Bhxqvu7mnEoPDlOq5PC0YRoGPJLY88p3KgUlecnw3Y4/CBVgs+nqpVQSCkig4PK9ayEEMojS89GBMgUfcMLv45dGDRBXr+fur1sXB0CX2/PEcicFKoDN8tiIOQcZZ7rs56BPDwxPpuPxtmQdBTiP1vTNei0FI3bH8OCRQQkUXbPq66hWEwlGnt/yvmwhBDCG0fPTgzQGHW3A7eSaSA0PVqzm77BeGQc9ltrzyxnUoBSh9y/HajDwGGGO56+mjUhEKS6Pi8bpoHgU0jNTy0H4wBiFzxPDhlEILElux6eyrWRUJQ5vd88FwJAQug8/y1oY3Bxtpve3nnUsODFKp5PC0YxkGOpHY88p5KwUlecnw3Y9ACBVgs+nqpVQSCkig4PK9ayEEMojS89GBMgUfcMLv45dGDRBXr+fur1wWB0CX2/PGcScEKn/M8dqKOQcYZrvs56BPDgxOpePxtmQdBTeP1vTNei0FInbG7+GSQgkTXbPq66lXEwhFnd7zv20jBS+F0PPUhDUFHG3A7eSaSQ0OVKzm77FfGAc9lNrzyHUpBCd9y/HajDwGGGO56+mjUhEKS6Li8bpoHgU0jNTy0H4wBiFzw+/hlUQKEVux6eyrWhQIQ5vd88NxJQUsgs/y1oY3Bxtpve3nnUsODFGo5PC1YxkGOpHY88p5KwUlecnw3Y9ACBVgs+nqpVQSCkig4PK9ayEEMojS89GBMgUfcMLv45dGDRBXr+fur1wWB0CX2/PGcScEKn/M8dqKOQcYZrvs56BPDgxOpd');
                        audio.play();
                    `);

                    // Show a notification
                    if ('Notification' in window && Notification.permission === 'granted') {
                        new Notification('Time Limit Reached', {
                            body: limitMessage,
                            icon: 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24"><path fill="%23d32f2f" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z"/></svg>'
                        });
                    }
                }
            }
        }

        // Update custom limits list in settings
        function updateCustomLimitsList() {
            customLimitsList.innerHTML = '';

            if (Object.keys(timeLimits.custom).length === 0) {
                customLimitsList.innerHTML = '<div style="padding:10px; color:#666; text-align:center;">No custom limits set.</div>';
                return;
            }

            for (const domain in timeLimits.custom) {
                const limit = timeLimits.custom[domain];
                const usage = timeUsage.custom[domain] || 0;

                const limitItem = document.createElement('div');
                limitItem.style.display = 'flex';
                limitItem.style.alignItems = 'center';
                limitItem.style.justifyContent = 'space-between';
                limitItem.style.padding = '10px';
                limitItem.style.background = '#f5f5f5';
                limitItem.style.borderRadius = '4px';
                limitItem.style.marginBottom = '8px';

                limitItem.innerHTML = `
                    <div>
                        <strong>${domain}</strong>
                        <div style="color:#666; font-size:12px;">Used: ${Math.floor(usage)} / ${limit} minutes</div>
                    </div>
                    <div>
                        <button class="edit-limit" data-domain="${domain}">Edit</button>
                        <button class="delete-limit" data-domain="${domain}">Delete</button>
                    </div>
                `;

                customLimitsList.appendChild(limitItem);
            }

            // Add event listeners to edit/delete buttons
            document.querySelectorAll('.edit-limit').forEach(button => {
                button.addEventListener('click', () => {
                    const domain = button.dataset.domain;
                    customDomain.value = domain;
                    customMinutes.value = timeLimits.custom[domain];
                });
            });

            document.querySelectorAll('.delete-limit').forEach(button => {
                button.addEventListener('click', () => {
                    const domain = button.dataset.domain;
                    delete timeLimits.custom[domain];
                    updateCustomLimitsList();
                });
            });
        }

        // Event listeners for time limit functionality

        // Save time limits
        saveTimeLimits.addEventListener('click', () => {
            // Update predefined limits
            timeLimits.youtube = parseInt(limitYoutube.value, 10) || 60;
            timeLimits.facebook = parseInt(limitFacebook.value, 10) || 25;
            timeLimits.total = parseInt(limitTotal.value, 10) || 130;

            // Save to localStorage
            saveTimeLimitsToStorage();

            // Show confirmation and restart (like other settings)
            alert('Time limits saved! Click OK to restart the browser.');
            settingsModal.style.display = 'none';
            try {
                const electron = require('electron');
                electron.ipcRenderer.send('restart-app');
            } catch (e) {
                console.error('Error restarting app:', e);
                window.location.href = 'app.html';
            }
        });

        // Add custom limit
        addCustomLimit.addEventListener('click', () => {
            const domain = customDomain.value.trim();
            const minutes = parseInt(customMinutes.value, 10);

            if (!domain) {
                alert('Please enter a domain name');
                return;
            }

            if (!minutes || minutes < 1) {
                alert('Please enter a valid time limit (at least 1 minute)');
                return;
            }

            // Add or update custom limit
            timeLimits.custom[domain] = minutes;

            // Initialize usage counter if needed
            if (!timeUsage.custom[domain]) {
                timeUsage.custom[domain] = 0;
            }

            // Update UI
            updateCustomLimitsList();

            // Clear form
            customDomain.value = '';
            customMinutes.value = '';
        });

        // Reset buttons
        resetYoutube.addEventListener('click', () => {
            timeUsage.youtube = 0;
            saveTimeUsage();
            updateTimerDisplay();
            updateCustomLimitsList();
            alert('YouTube timer reset!');
        });

        resetFacebook.addEventListener('click', () => {
            timeUsage.facebook = 0;
            saveTimeUsage();
            updateTimerDisplay();
            updateCustomLimitsList();
            alert('Facebook timer reset!');
        });

        resetTotal.addEventListener('click', () => {
            timeUsage.total = 0;
            saveTimeUsage();
            updateTimerDisplay();
            updateCustomLimitsList();
            alert('Total usage timer reset!');
        });

        // Show/hide timer setting
        showTimer.addEventListener('change', () => {
            localStorage.setItem('showTimer', showTimer.checked);
            timerDisplay.style.display = showTimer.checked ? 'flex' : 'none';
        });

        // Timer display is now just informational - no click action
        timerDisplay.addEventListener('click', function() {
            // Do nothing - timer display is now just informational
            // This prevents accidental access to time limit settings
            console.log('Timer display clicked - no action taken');
        });

        // Function to switch to a specific tab
        function switchToTab(tabName) {
            // Set active tab
            document.querySelectorAll('.settings-tab').forEach(tab => {
                tab.classList.toggle('active', tab.dataset.tab === tabName);
            });
            // Show corresponding panel
            document.querySelectorAll('.settings-panel').forEach(panel => {
                panel.style.display = panel.id === `panel-${tabName}` ? 'block' : 'none';
            });
            // Special logic for certain tabs
            if (tabName === 'time-limits') {
                try {
                    const saved = JSON.parse(localStorage.getItem('timeLimits')) || {};
                    timeLimits.custom = saved.custom || {};
                    updateCustomLimitsList();
                } catch (e) {
                    console.error('Failed to reset custom limits:', e);
                }
            }
            if (tabName === 'parental-control') {
                loadParentalControlSettings();
            }
            if (tabName === 'websites-cookies') {
                loadWebsitesCookiesSettings();
            }
            if (tabName === 'advanced') {
                loadAdvancedSettings();
            }
        }

        // Get password status element
        const passwordStatus = document.getElementById('password-status');
        const passwordForm = document.getElementById('password-form');

        // Update password status display - improved with direct localStorage check
        function updatePasswordStatus() {
            console.log('Updating password status');

            // Direct check of localStorage for password
            const hasPassword = !!localStorage.getItem('password');
            console.log('Password in localStorage:', hasPassword);

            if (passwordStatus) {
                if (hasPassword) {
                    passwordStatus.textContent = 'Status: Password protection is enabled';
                    passwordStatus.style.color = '#4caf50';
                    console.log('Setting status to enabled');
                } else {
                    passwordStatus.textContent = 'Status: No password protection';
                    passwordStatus.style.color = '#757575';
                    console.log('Setting status to disabled');
                }
            }

            // Update delete button visibility
            if (deletePasswordBtn) {
                deletePasswordBtn.style.display = hasPassword ? 'block' : 'none';
                console.log('Delete button visibility:', hasPassword ? 'visible' : 'hidden');
            }
        }

        // Password modal event listeners
        submitPassword.addEventListener('click', () => {
            const enteredPassword = passwordInput.value;
            const success = verifyPassword(enteredPassword);

            if (success) {
                passwordModal.style.display = 'none';

                // Execute the stored callback
                if (currentPasswordCallback && typeof currentPasswordCallback === 'function') {
                    currentPasswordCallback(true);
                    // Clear the callback after use
                    currentPasswordCallback = null;
                }
            } else {
                passwordError.style.display = 'block';
                passwordInput.value = '';
                passwordInput.focus();
            }
        });

        // Handle form submission
        passwordForm.addEventListener('submit', (e) => {
            e.preventDefault();
            submitPassword.click();
        });

        cancelPassword.addEventListener('click', () => {
            passwordModal.style.display = 'none';
            // Clear the callback when canceled
            currentPasswordCallback = null;
        });



        // Set up the delete password button with a self-executing function
        (function() {
            // Get the button element
            var deleteBtn = document.getElementById('delete-password');

            // Define the function to handle the click
            function handleDeletePassword() {
                // Get current password
                var currentPassword = localStorage.getItem('password');

                if (!currentPassword) {
                    return; // No password set
                }

                // Show the password modal
                if (passwordModal && passwordPromptMessage) {
                    // Set the message
                    passwordPromptMessage.textContent = 'Enter your password to delete password protection:';

                    // Show the modal
                    passwordModal.style.display = 'flex';

                    // Clear any previous input
                    if (passwordInput) {
                        passwordInput.value = '';
                        passwordInput.focus();
                    }

                    // Hide any previous error
                    if (passwordError) {
                        passwordError.style.display = 'none';
                    }

                    // Set up a callback for when the password is submitted
                    currentPasswordCallback = function(success) {
                        if (success) {
                            try {
                                // Remove password
                                localStorage.removeItem('password');

                                // Update UI
                                var status = document.getElementById('password-status');
                                status.textContent = 'Status: No password protection';
                                status.style.color = '#757575';

                                // Hide the delete button immediately
                                var deleteBtn = document.getElementById('delete-password');
                                if (deleteBtn) deleteBtn.style.display = 'none';

                                // Also update password status everywhere
                                if (typeof updatePasswordStatus === 'function') updatePasswordStatus();
                            } catch (err) {
                                console.error('Error deleting password:', err);
                            }
                        }
                    };
                }
            }

            // Add the event listener
            if (deleteBtn) {
                // Remove any existing listeners
                deleteBtn.removeEventListener('click', handleDeletePassword);

                // Add the new listener
                deleteBtn.addEventListener('click', handleDeletePassword);
            }
        })();

        // Set up the save password button with a direct approach
        (function() {
            // Get the button element
            var saveBtn = document.getElementById('save-password-new');

            // Define the function to handle the click
            function handleSavePassword() {
                // Get the password value
                var pwd = document.getElementById('setting-password').value.trim();
                var confirmPwd = document.getElementById('confirm-setting-password').value.trim();

                // Hide previous error
                passwordErrorMsg.style.display = 'none';
                passwordErrorMsg.textContent = '';

                // Only save if both fields are filled and match
                if (!pwd || !confirmPwd) {
                    passwordErrorMsg.textContent = 'Please enter and confirm your password.';
                    passwordErrorMsg.style.display = 'block';
                    return;
                }
                if (pwd !== confirmPwd) {
                    passwordErrorMsg.textContent = 'Passwords do not match. Please try again.';
                    passwordErrorMsg.style.display = 'block';
                    return;
                }

                // Save to localStorage
                localStorage.setItem('password', pwd);

                // Update status
                var status = document.getElementById('password-status');
                status.textContent = 'Status: Password protection is enabled';
                status.style.color = '#4caf50';

                // Show delete button
                document.getElementById('delete-password').style.display = 'block';

                // Clear input
                document.getElementById('setting-password').value = '';
                document.getElementById('confirm-setting-password').value = '';
                passwordErrorMsg.style.display = 'none';
            }

            // Add the event listener
            if (saveBtn) {
                // Remove any existing listeners
                saveBtn.removeEventListener('click', handleSavePassword);

                // Add the new listener
                saveBtn.addEventListener('click', handleSavePassword);
            }
        })();

        // Initialize password status on page load
        updatePasswordStatus();

        // Show/hide remaining time setting
        showRemainingTime.addEventListener('change', () => {
            localStorage.setItem('showRemainingTime', showRemainingTime.checked);
            updateTimerDisplay();
        });

        // Parental Control Elements
        const blockImages = document.getElementById('block-images');
        const imageWhitelistDomain = document.getElementById('image-whitelist-domain');
        const addImageWhitelist = document.getElementById('add-image-whitelist');
        const imageWhitelistContainer = document.getElementById('image-whitelist');

        const blockVideos = document.getElementById('block-videos');
        const videoWhitelistDomain = document.getElementById('video-whitelist-domain');
        const addVideoWhitelist = document.getElementById('add-video-whitelist');
        const videoWhitelistContainer = document.getElementById('video-whitelist');

        const blockDownloads = document.getElementById('block-downloads');
        const downloadWhitelistExt = document.getElementById('download-whitelist-ext');
        const addDownloadWhitelist = document.getElementById('add-download-whitelist');
        const downloadWhitelistContainer = document.getElementById('download-whitelist');

        const saveParentalControl = document.getElementById('save-parental-control');

        // Load parental control settings
        function loadParentalControlSettings() {
            // Load image blocking settings
            blockImages.checked = localStorage.getItem('blockImages') === 'true';
            const imageWhitelist = JSON.parse(localStorage.getItem('imageWhitelist') || '[]');
            imageWhitelistContainer.innerHTML = '';
            imageWhitelist.forEach(domain => {
                addWhitelistItem(domain, imageWhitelistContainer, 'image');
            });

            // Load video blocking settings
            blockVideos.checked = localStorage.getItem('blockVideos') === 'true';
            const videoWhitelist = JSON.parse(localStorage.getItem('videoWhitelist') || '[]');
            videoWhitelistContainer.innerHTML = '';
            videoWhitelist.forEach(domain => {
                addWhitelistItem(domain, videoWhitelistContainer, 'video');
            });

            // Load download blocking settings
            blockDownloads.checked = localStorage.getItem('blockDownloads') === 'true';
            const downloadWhitelist = JSON.parse(localStorage.getItem('downloadWhitelist') || '[]');
            downloadWhitelistContainer.innerHTML = '';
            downloadWhitelist.forEach(ext => {
                addWhitelistItem(ext, downloadWhitelistContainer, 'download');
            });
        }

        // Add whitelist item to container with improved UI
        function addWhitelistItem(value, container, type) {
            // Normalize the domain for display
            let displayValue = value;
            if (type === 'image' || type === 'video') {
                displayValue = normalizeDomain(value);
            }

            const item = document.createElement('div');
            item.className = 'whitelist-item';
            item.style.display = 'flex';
            item.style.justifyContent = 'space-between';
            item.style.alignItems = 'center';
            item.style.padding = '10px';
            item.style.margin = '8px 0';
            item.style.backgroundColor = '#f5f5f5';
            item.style.borderRadius = '6px';
            item.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
            item.style.transition = 'all 0.2s ease';

            // Hover effect
            item.addEventListener('mouseover', () => {
                item.style.backgroundColor = '#e0e0e0';
            });

            item.addEventListener('mouseout', () => {
                item.style.backgroundColor = '#f5f5f5';
            });

            // Left side with icon and text
            const leftSide = document.createElement('div');
            leftSide.style.display = 'flex';
            leftSide.style.alignItems = 'center';

            // Add appropriate icon based on type
            const icon = document.createElement('span');
            icon.style.marginRight = '10px';
            icon.style.fontSize = '18px';

            if (type === 'image') {
                icon.innerHTML = '🖼️';
                icon.title = 'Image whitelist';
            } else if (type === 'video') {
                icon.innerHTML = '🎬';
                icon.title = 'Video whitelist';
            } else {
                icon.innerHTML = '📁';
                icon.title = 'Download whitelist';
            }

            leftSide.appendChild(icon);

            // Text with domain/value
            const text = document.createElement('span');
            text.textContent = displayValue;
            text.style.fontWeight = '500';
            leftSide.appendChild(text);

            // Remove button with improved styling
            const removeBtn = document.createElement('button');
            removeBtn.innerHTML = '&times;'; // × symbol
            removeBtn.title = 'Remove from whitelist';
            removeBtn.style.padding = '4px 10px';
            removeBtn.style.backgroundColor = '#f44336';
            removeBtn.style.color = 'white';
            removeBtn.style.border = 'none';
            removeBtn.style.borderRadius = '4px';
            removeBtn.style.cursor = 'pointer';
            removeBtn.style.fontSize = '16px';
            removeBtn.style.fontWeight = 'bold';
            removeBtn.style.transition = 'background-color 0.2s ease';

            // Hover effect for button
            removeBtn.addEventListener('mouseover', () => {
                removeBtn.style.backgroundColor = '#d32f2f';
            });

            removeBtn.addEventListener('mouseout', () => {
                removeBtn.style.backgroundColor = '#f44336';
            });

            // ABSOLUTE MINIMAL remove button handler - just remove the item
            removeBtn.addEventListener('click', function() {
                // Just remove the item - don't save to localStorage
                // This prevents freezing by deferring the save until the Save Changes button is clicked
                item.remove();
            });

            item.appendChild(leftSide);
            item.appendChild(removeBtn);

            // Add with fade-in animation
            item.style.opacity = '0';
            item.style.transform = 'translateX(-20px)';
            container.appendChild(item);

            // Trigger animation
            setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
            }, 10);
        }

        // Improved saveWhitelist function with storage event triggering and main process synchronization
        function saveWhitelist(type) {
            try {
                // Get container based on type
                var container = null;
                if (type === 'image') {
                    container = imageWhitelistContainer;
                } else if (type === 'video') {
                    container = videoWhitelistContainer;
                } else {
                    container = downloadWhitelistContainer;
                }

                // Get items directly from DOM with minimal processing
                var items = [];
                var elements = container.querySelectorAll('.whitelist-item span:not(:first-child)');
                for (var i = 0; i < elements.length; i++) {
                    items.push(elements[i].textContent);
                }

                // Get the current value from localStorage
                const currentValue = localStorage.getItem(type + 'Whitelist');
                const newValue = JSON.stringify(items);

                // Only update if the value has changed
                if (currentValue !== newValue) {
                    console.log(`Saving ${type} whitelist:`, items);

                    // Save to localStorage
                    localStorage.setItem(type + 'Whitelist', newValue);

                    // Trigger a storage event to notify other tabs/windows
                    // This is needed because storage events don't fire in the same window that makes the change
                    if (type === 'image' || type === 'video') {
                        // Create and dispatch a storage event
                        const storageEvent = new StorageEvent('storage', {
                            key: type + 'Whitelist',
                            newValue: newValue,
                            oldValue: currentValue,
                            storageArea: localStorage
                        });
                        window.dispatchEvent(storageEvent);

                        console.log(`Dispatched storage event for ${type}Whitelist:`, items);

                        // Synchronize with main process
                        syncWhitelistWithMainProcess(type);
                    }
                }

                return items;
            } catch (e) {
                console.error('Error in saveWhitelist:', e);
                return [];
            }
        }

        // Helper function to add domain to whitelist with validation - ENHANCED VERSION
        function addDomainToWhitelist(inputElement, container, type) {
            let value = inputElement.value.trim();

            if (!value) {
                // Show error if empty
                inputElement.style.borderColor = '#f44336';

                // Show error message
                showTooltip(inputElement, 'Please enter a domain', 'error');

                setTimeout(() => {
                    inputElement.style.borderColor = '';
                }, 2000);
                return;
            }

            // For domains, normalize and validate
            if (type === 'image' || type === 'video') {
                try {
                    // Handle different input formats
                    if (value.includes('://')) {
                        // Full URL format (https://example.com)
                        try {
                            const url = new URL(value);
                            value = url.hostname;
                            console.log('Extracted hostname from URL:', value);
                        } catch (e) {
                            console.error('Error parsing URL:', e);
                            // If URL parsing fails, try to extract domain manually
                            const match = value.match(/:\/\/(.[^/]+)/);
                            if (match && match[1]) {
                                value = match[1];
                                console.log('Manually extracted hostname:', value);
                            }
                        }
                    } else if (value.startsWith('www.')) {
                        // www format (www.example.com) - keep as is
                        console.log('Domain starts with www:', value);
                    } else if (!value.includes('.')) {
                        // Single word (google) - add .com
                        value = value + '.com';
                        console.log('Added .com to domain:', value);
                        showTooltip(inputElement, `Added as ${value}`, 'info');
                    }

                    // Normalize the domain
                    const originalValue = value;
                    value = normalizeDomain(value);
                    console.log('Normalized domain:', originalValue, '→', value);

                    // Store the original value for display but use normalized for matching
                    const displayValue = value;

                    // Check for duplicates in the DOM
                    const domItems = Array.from(container.querySelectorAll('.whitelist-item span:last-child'))
                        .map(el => el.textContent.toLowerCase().trim());

                    // Also normalize the DOM items for comparison
                    const normalizedDomItems = domItems.map(item => normalizeDomain(item));

                    // Check if domain is already in whitelist (exact match or subdomain match)
                    const isDuplicate = normalizedDomItems.some(item => {
                        return value.toLowerCase() === item.toLowerCase() ||
                               value.toLowerCase().endsWith('.' + item.toLowerCase()) ||
                               item.toLowerCase().endsWith('.' + value.toLowerCase());
                    });

                    if (isDuplicate) {
                        inputElement.style.borderColor = '#f44336';
                        showTooltip(inputElement, 'Domain already in whitelist', 'error');
                        setTimeout(() => {
                            inputElement.style.borderColor = '';
                        }, 2000);
                        return;
                    }

                    // Log the whitelist for debugging
                    console.log('Current whitelist items:', domItems);
                    console.log('Adding to whitelist:', value);

                    // Test if the domain would be matched by the whitelist function
                    const testWhitelist = [...normalizedDomItems, value];
                    const wouldMatch = isDomainWhitelisted_NEW(value, testWhitelist);
                    console.log(`Domain ${value} would ${wouldMatch ? '' : 'NOT '}match in whitelist:`, testWhitelist);

                    if (!wouldMatch) {
                        console.warn(`WARNING: Domain ${value} might not match correctly in the whitelist!`);
                    }
                } catch (e) {
                    console.error('Error processing domain:', e);
                    // If there's an error, just use the input as is
                    value = normalizeDomain(value);
                    console.log('Using normalized value after error:', value);
                }
            } else if (type === 'download') {
                // For download extensions, clean up
                // Remove leading dot if present
                value = value.toLowerCase();
                value = value.startsWith('.') ? value.substring(1) : value;
                console.log('Normalized download extension:', value);
            }

            // Add to whitelist
            addWhitelistItem(value, container, type);
            inputElement.value = '';

            // Save to localStorage and get the updated whitelist
            const savedItems = saveWhitelist(type);

            // Explicitly synchronize with main process
            syncWhitelistWithMainProcess(type);

            console.log(`Added domain ${value} to ${type} whitelist and synchronized with main process`);
            console.log(`Current ${type} whitelist:`, savedItems);

            // Show success message
            showTooltip(inputElement, `Added ${value} to whitelist`, 'success');
        }

        // Helper function to show tooltips
        function showTooltip(element, message, type) {
            // Create tooltip
            const tooltip = document.createElement('div');
            tooltip.textContent = message;
            tooltip.style.position = 'absolute';
            tooltip.style.padding = '5px 10px';
            tooltip.style.borderRadius = '4px';
            tooltip.style.fontSize = '12px';
            tooltip.style.zIndex = '1000';
            tooltip.style.top = (element.offsetTop + element.offsetHeight + 5) + 'px';
            tooltip.style.left = element.offsetLeft + 'px';
            tooltip.style.transition = 'opacity 0.3s ease';
            tooltip.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';

            // Set colors based on type
            if (type === 'error') {
                tooltip.style.backgroundColor = '#f44336';
                tooltip.style.color = 'white';
            } else if (type === 'success') {
                tooltip.style.backgroundColor = '#4caf50';
                tooltip.style.color = 'white';
            } else {
                tooltip.style.backgroundColor = '#2196f3';
                tooltip.style.color = 'white';
            }

            // Add tooltip to DOM
            element.parentNode.appendChild(tooltip);

            // Fade in
            tooltip.style.opacity = '0';
            setTimeout(() => {
                tooltip.style.opacity = '1';
            }, 10);

            // Remove tooltip after delay
            setTimeout(() => {
                tooltip.style.opacity = '0';
                setTimeout(() => {
                    if (tooltip.parentNode) {
                        tooltip.parentNode.removeChild(tooltip);
                    }
                }, 300);
            }, 2000);
        }

        // Add image whitelist domain with improved handling
        addImageWhitelist.addEventListener('click', () => {
            addDomainToWhitelist(imageWhitelistDomain, imageWhitelistContainer, 'image');
        });

        // Also allow pressing Enter to add domain
        imageWhitelistDomain.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                addDomainToWhitelist(imageWhitelistDomain, imageWhitelistContainer, 'image');
            }
        });

        // Add video whitelist domain with improved handling
        addVideoWhitelist.addEventListener('click', () => {
            addDomainToWhitelist(videoWhitelistDomain, videoWhitelistContainer, 'video');
        });

        // Also allow pressing Enter to add domain
        videoWhitelistDomain.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                addDomainToWhitelist(videoWhitelistDomain, videoWhitelistContainer, 'video');
            }
        });

        // Add download whitelist extension with improved handling
        addDownloadWhitelist.addEventListener('click', () => {
            addDomainToWhitelist(downloadWhitelistExt, downloadWhitelistContainer, 'download');
        });

        // Also allow pressing Enter to add extension
        downloadWhitelistExt.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                addDomainToWhitelist(downloadWhitelistExt, downloadWhitelistContainer, 'download');
            }
        });

        // Enhanced debug function to test whitelist matching
        function testWhitelistMatching() {
            // Get the current image whitelist from localStorage
            const currentImageWhitelist = JSON.parse(localStorage.getItem('imageWhitelist') || '[]');
            console.log('DEBUG: Current image whitelist from localStorage:', currentImageWhitelist);

            // Test domains - include the problematic domain
            const testDomains = [
                'google.com',
                'www.google.com',
                'mail.google.com',
                'youtube.com',
                'facebook.com',
                'divinerevelations.info',
                'www.divinerevelations.info'
            ];

            // Add any custom domain from the input field
            const customDomain = document.getElementById('test-whitelist-domain');
            if (customDomain && customDomain.value.trim()) {
                testDomains.push(customDomain.value.trim());
            }

            console.log('DEBUG: Testing whitelist matching with current whitelist...');
            testDomains.forEach(domain => {
                const result = isDomainWhitelisted_NEW(domain, currentImageWhitelist);
                console.log(`DEBUG: Domain "${domain}" is whitelisted: ${result}`);
            });

            // Also print the actual whitelist items for verification
            console.log('DEBUG: Whitelist items in localStorage:');
            try {
                const storedWhitelist = JSON.parse(localStorage.getItem('imageWhitelist') || '[]');
                storedWhitelist.forEach((item, index) => {
                    console.log(`DEBUG: Whitelist item ${index}: "${item}"`);

                    // Test if this item would match itself (sanity check)
                    const selfMatch = isDomainWhitelisted_NEW(item, [item]);
                    console.log(`DEBUG: Whitelist item "${item}" matches itself: ${selfMatch}`);

                    if (!selfMatch) {
                        console.error(`DEBUG: CRITICAL ERROR - Whitelist item "${item}" doesn't match itself!`);
                    }
                });
            } catch (e) {
                console.error('DEBUG: Error parsing whitelist from localStorage:', e);
            }

            // Show results in the UI
            const resultElement = document.getElementById('test-whitelist-results');
            if (resultElement) {
                let resultsHtml = '<h4>Whitelist Test Results:</h4><ul>';

                // Show current whitelist
                resultsHtml += '<li><strong>Current Whitelist:</strong> ';
                if (currentImageWhitelist.length === 0) {
                    resultsHtml += '<em>Empty</em>';
                } else {
                    resultsHtml += '<ul>';
                    currentImageWhitelist.forEach(item => {
                        resultsHtml += `<li>${item}</li>`;
                    });
                    resultsHtml += '</ul>';
                }
                resultsHtml += '</li>';

                // Show test results
                resultsHtml += '<li><strong>Test Results:</strong><ul>';
                testDomains.forEach(domain => {
                    const result = isDomainWhitelisted_NEW(domain, currentImageWhitelist);
                    const color = result ? 'green' : 'red';
                    resultsHtml += `<li style="color:${color}">${domain}: ${result ? '✓ Whitelisted' : '✗ Not whitelisted'}</li>`;
                });
                resultsHtml += '</ul></li>';

                resultsHtml += '</ul>';
                resultElement.innerHTML = resultsHtml;
                resultElement.style.display = 'block';
            }
        }

        // Add a debug UI for testing whitelist functionality
        function addWhitelistDebugUI() {
            // Create the debug UI container
            const debugContainer = document.createElement('div');
            debugContainer.id = 'whitelist-debug-container';
            debugContainer.style.marginTop = '20px';
            debugContainer.style.padding = '15px';
            debugContainer.style.backgroundColor = '#f8f9fa';
            debugContainer.style.border = '1px solid #ddd';
            debugContainer.style.borderRadius = '5px';

            // Add a heading
            const heading = document.createElement('h3');
            heading.textContent = 'Whitelist Debug Tools';
            heading.style.marginTop = '0';
            debugContainer.appendChild(heading);

            // Add a description
            const description = document.createElement('p');
            description.textContent = 'Use these tools to test and debug the whitelist functionality.';
            debugContainer.appendChild(description);

            // Add a test domain input
            const inputGroup = document.createElement('div');
            inputGroup.style.display = 'flex';
            inputGroup.style.marginBottom = '10px';

            const input = document.createElement('input');
            input.type = 'text';
            input.id = 'test-whitelist-domain';
            input.placeholder = 'Enter domain to test (e.g., example.com)';
            input.style.flex = '1';
            input.style.padding = '8px';
            input.style.border = '1px solid #ddd';
            input.style.borderRadius = '4px 0 0 4px';
            inputGroup.appendChild(input);

            const button = document.createElement('button');
            button.textContent = 'Test Whitelist';
            button.style.padding = '8px 15px';
            button.style.backgroundColor = '#4CAF50';
            button.style.color = 'white';
            button.style.border = 'none';
            button.style.borderRadius = '0 4px 4px 0';
            button.style.cursor = 'pointer';
            button.onclick = testWhitelistMatching;
            inputGroup.appendChild(button);

            debugContainer.appendChild(inputGroup);

            // Add a results container
            const results = document.createElement('div');
            results.id = 'test-whitelist-results';
            results.style.marginTop = '10px';
            results.style.padding = '10px';
            results.style.backgroundColor = 'white';
            results.style.border = '1px solid #ddd';
            results.style.borderRadius = '4px';
            results.style.display = 'none';
            debugContainer.appendChild(results);

            // Add a button to add a test domain to the whitelist
            const divineButton = document.createElement('button');
            divineButton.textContent = 'Add example.com to Whitelist';
            divineButton.style.marginTop = '15px';
            divineButton.style.padding = '8px 16px';
            divineButton.style.backgroundColor = '#4CAF50';
            divineButton.style.color = 'white';
            divineButton.style.border = 'none';
            divineButton.style.borderRadius = '4px';
            divineButton.style.cursor = 'pointer';
            divineButton.style.display = 'block';
            divineButton.style.width = '100%';

            // Add status display
            const divineStatus = document.createElement('div');
            divineStatus.id = 'divine-revelations-status';
            divineStatus.style.marginTop = '5px';
            divineStatus.style.fontStyle = 'italic';

            // Add event listener for the button
            divineButton.addEventListener('click', function() {
                try {
                    // Get current whitelist
                    const currentWhitelist = JSON.parse(localStorage.getItem('imageWhitelist') || '[]');

                    // Check if example.com is already in the whitelist
                    if (currentWhitelist.includes('example.com')) {
                        divineStatus.textContent = 'example.com is already in the whitelist!';
                        divineStatus.style.color = '#FFA500';
                        return;
                    }

                    // Add example.com to the whitelist
                    currentWhitelist.push('example.com');

                    // Save the updated whitelist
                    localStorage.setItem('imageWhitelist', JSON.stringify(currentWhitelist));

                    // Update status
                    divineStatus.textContent = 'Successfully added example.com to the whitelist!';
                    divineStatus.style.color = '#4CAF50';

                    // Run the debug function to verify
                    setTimeout(debugWhitelist, 500);

                    // Refresh the whitelist UI
                    setTimeout(() => {
                        // Clear the current whitelist UI
                        const imageWhitelistContainer = document.getElementById('image-whitelist');
                        if (imageWhitelistContainer) {
                            imageWhitelistContainer.innerHTML = '';

                            // Re-add all items
                            currentWhitelist.forEach(domain => {
                                addWhitelistItem(domain, imageWhitelistContainer, 'image');
                            });
                        }
                    }, 1000);

                    // Synchronize with main process
                    syncWhitelistWithMainProcess('image');
                } catch (e) {
                    console.error('Error adding example.com to whitelist:', e);
                    divineStatus.textContent = 'Error: ' + e.message;
                    divineStatus.style.color = '#FF0000';
                }
            });

            // Add a button to add another test domain to the whitelist
            const nuNlButton = document.createElement('button');
            nuNlButton.textContent = 'Add test.com to Whitelist';
            nuNlButton.style.marginTop = '15px';
            nuNlButton.style.padding = '8px 16px';
            nuNlButton.style.backgroundColor = '#2196F3';
            nuNlButton.style.color = 'white';
            nuNlButton.style.border = 'none';
            nuNlButton.style.borderRadius = '4px';
            nuNlButton.style.cursor = 'pointer';
            nuNlButton.style.display = 'block';
            nuNlButton.style.width = '100%';

            // Add a button to test the whitelist functionality
            const testWhitelistButton = document.createElement('button');
            testWhitelistButton.textContent = 'Test Whitelist Functionality';
            testWhitelistButton.style.marginTop = '15px';
            testWhitelistButton.style.padding = '8px 16px';
            testWhitelistButton.style.backgroundColor = '#FF9800';
            testWhitelistButton.style.color = 'white';
            testWhitelistButton.style.border = 'none';
            testWhitelistButton.style.borderRadius = '4px';
            testWhitelistButton.style.cursor = 'pointer';
            testWhitelistButton.style.display = 'block';
            testWhitelistButton.style.width = '100%';

            // Add status display for nu.nl
            const nuNlStatus = document.createElement('div');
            nuNlStatus.id = 'nu-nl-status';
            nuNlStatus.style.marginTop = '5px';
            nuNlStatus.style.fontStyle = 'italic';

            // Add event listener for the test.com button
            nuNlButton.addEventListener('click', function() {
                try {
                    // Get current whitelist
                    const currentWhitelist = JSON.parse(localStorage.getItem('imageWhitelist') || '[]');

                    // Check if test.com is already in the whitelist
                    if (currentWhitelist.includes('test.com')) {
                        nuNlStatus.textContent = 'test.com is already in the whitelist!';
                        nuNlStatus.style.color = '#FFA500';
                        return;
                    }

                    // Add test.com to the whitelist
                    currentWhitelist.push('test.com');

                    // Save the updated whitelist
                    localStorage.setItem('imageWhitelist', JSON.stringify(currentWhitelist));

                    // Update status
                    nuNlStatus.textContent = 'Successfully added test.com to the whitelist!';
                    nuNlStatus.style.color = '#4CAF50';

                    // Run the debug function to verify
                    setTimeout(debugWhitelist, 500);

                    // Refresh the whitelist UI
                    setTimeout(() => {
                        // Clear the current whitelist UI
                        const imageWhitelistContainer = document.getElementById('image-whitelist');
                        if (imageWhitelistContainer) {
                            imageWhitelistContainer.innerHTML = '';

                            // Re-add all items
                            currentWhitelist.forEach(domain => {
                                addWhitelistItem(domain, imageWhitelistContainer, 'image');
                            });
                        }
                    }, 1000);

                    // Synchronize with main process
                    syncWhitelistWithMainProcess('image');
                } catch (e) {
                    console.error('Error adding test.com to whitelist:', e);
                    nuNlStatus.textContent = 'Error: ' + e.message;
                    nuNlStatus.style.color = '#FF0000';
                }
            });

            // Add status display for test whitelist button
            const testWhitelistStatus = document.createElement('div');
            testWhitelistStatus.id = 'test-whitelist-status';
            testWhitelistStatus.style.marginTop = '5px';
            testWhitelistStatus.style.fontStyle = 'italic';

            // Add event listener for the test whitelist button
            testWhitelistButton.addEventListener('click', function() {
                try {
                    // Get current whitelist
                    const currentWhitelist = JSON.parse(localStorage.getItem('imageWhitelist') || '[]');

                    // Synchronize with main process
                    syncWhitelistWithMainProcess('image');

                    // Update status
                    testWhitelistStatus.textContent = 'Whitelist synchronized with main process. Check console for details.';
                    testWhitelistStatus.style.color = '#4CAF50';

                    // Run the debug function to verify
                    const debugResult = debugWhitelist();

                    // Display the debug result
                    console.log('Debug whitelist result:', debugResult);

                    // Add a test domain if it doesn't exist
                    const testDomain = 'example.com';
                    if (!currentWhitelist.includes(testDomain)) {
                        currentWhitelist.push(testDomain);
                        localStorage.setItem('imageWhitelist', JSON.stringify(currentWhitelist));

                        // Synchronize with main process
                        syncWhitelistWithMainProcess('image');

                        // Update status
                        testWhitelistStatus.textContent += `\nAdded ${testDomain} to whitelist and synchronized.`;

                        // Refresh the whitelist UI
                        setTimeout(() => {
                            // Clear the current whitelist UI
                            const imageWhitelistContainer = document.getElementById('image-whitelist');
                            if (imageWhitelistContainer) {
                                imageWhitelistContainer.innerHTML = '';

                                // Re-add all items
                                currentWhitelist.forEach(domain => {
                                    addWhitelistItem(domain, imageWhitelistContainer, 'image');
                                });
                            }
                        }, 1000);
                    }

                    // Test the whitelist functionality directly with the main process
                    try {
                        const electron = require('electron');
                        const testUrl = 'https://example.com/test.jpg';

                        electron.ipcRenderer.invoke('electronBrowser:should-block-images-for-url', testUrl)
                            .then(shouldBlock => {
                                console.log(`Test URL ${testUrl} should be blocked: ${shouldBlock}`);
                                testWhitelistStatus.textContent += `\nTest URL ${testUrl} should be blocked: ${shouldBlock}`;
                                testWhitelistStatus.style.color = shouldBlock ? '#FF0000' : '#4CAF50';
                            })
                            .catch(error => {
                                console.error('Error testing whitelist with main process:', error);
                                testWhitelistStatus.textContent += `\nError testing with main process: ${error.message}`;
                                testWhitelistStatus.style.color = '#FF0000';
                            });
                    } catch (e) {
                        console.error('Error invoking main process test:', e);
                    }
                } catch (e) {
                    console.error('Error testing whitelist functionality:', e);
                    testWhitelistStatus.textContent = 'Error: ' + e.message;
                    testWhitelistStatus.style.color = '#FF0000';
                }
            });

            // Add all buttons and status elements to the debug container
            debugContainer.appendChild(divineButton);
            debugContainer.appendChild(divineStatus);
            debugContainer.appendChild(nuNlButton);
            debugContainer.appendChild(nuNlStatus);
            debugContainer.appendChild(testWhitelistButton);
            debugContainer.appendChild(testWhitelistStatus);

            // Add the debug container to the image whitelist section
            const imageWhitelistSection = document.querySelector('#panel-parental-control .settings-section:nth-child(1)');
            if (imageWhitelistSection) {
                imageWhitelistSection.appendChild(debugContainer);
            }
        }

        // Call this function when the settings panel is loaded
        setTimeout(addWhitelistDebugUI, 1000);

        // Debug function to check the current whitelist
        function debugWhitelist() {
            try {
                // Get the current image whitelist from localStorage
                const imageWhitelist = JSON.parse(localStorage.getItem('imageWhitelist') || '[]');
                console.log('DEBUG WHITELIST: Current image whitelist:', imageWhitelist);

                // Test specific domains
                const testDomains = [
                    'chess.com',
                    'www.chess.com',
                    'divinerevelations.info',
                    'www.divinerevelations.info',
                    'nu.nl',
                    'www.nu.nl',
                    'example.com',
                    'www.example.com',
                    'test.example.com'
                ];

                console.log('DEBUG WHITELIST: Testing domains against whitelist...');
                testDomains.forEach(domain => {
                    const result = isDomainWhitelisted_NEW(domain, imageWhitelist);
                    console.log(`DEBUG WHITELIST: Domain "${domain}" is whitelisted: ${result}`);

                    // Test the IPC handler logic
                    const globalBlock = localStorage.getItem('blockImages') === 'true';
                    const shouldBlock = globalBlock && !result;
                    console.log(`DEBUG WHITELIST: IPC Handler simulation for ${domain}: GlobalBlock: ${globalBlock}, Whitelisted: ${result}, ShouldBlock: ${shouldBlock}`);
                });

                // No special domain handling - all domains must be explicitly whitelisted by the user
                console.log('DEBUG WHITELIST: No special domain handling - all domains must be explicitly whitelisted by the user');

                // Check the normalized form of divinerevelations.info
                const normalizedDomain1 = normalizeDomain('divinerevelations.info');
                console.log(`DEBUG WHITELIST: Normalized form of divinerevelations.info: ${normalizedDomain1}`);

                // Check the normalized form of nu.nl
                const normalizedDomain2 = normalizeDomain('nu.nl');
                console.log(`DEBUG WHITELIST: Normalized form of nu.nl: ${normalizedDomain2}`);

                return {
                    whitelist: imageWhitelist,
                    normalizedDomain1: normalizedDomain1,
                    normalizedDomain2: normalizedDomain2
                };
            } catch (e) {
                console.error('DEBUG WHITELIST: Error checking whitelist:', e);
                return { error: e.message };
            }
        }

        // General function to synchronize whitelist with main process
        function syncWhitelistWithMainProcess(type = 'image') {
            try {
                // Get current whitelist from localStorage
                const whitelistKey = type + 'Whitelist';
                const currentWhitelist = JSON.parse(localStorage.getItem(whitelistKey) || '[]');

                console.log(`Synchronizing ${type} whitelist with main process:`, currentWhitelist);

                // No special domain handling - only domains explicitly added by the user are in the whitelist

                // Notify the main process about the whitelist update
                try {
                    const electron = require('electron');
                    if (type === 'image') {
                        electron.ipcRenderer.send('update-whitelist', currentWhitelist);
                    } else if (type === 'video') {
                        electron.ipcRenderer.send('update-video-whitelist', currentWhitelist);
                    } else if (type === 'download') {
                        electron.ipcRenderer.send('update-download-whitelist', currentWhitelist);
                    }

                    console.log(`Successfully sent ${type} whitelist to main process:`, currentWhitelist);

                    // Add a callback to verify the whitelist was updated
                    if (type === 'image') {
                        electron.ipcRenderer.once('whitelist-updated', (event, response) => {
                            if (response.success) {
                                console.log('Main process confirmed whitelist update');
                            } else {
                                console.error('Main process reported error updating whitelist:', response.error);
                            }
                        });
                    }

                    return true;
                } catch (e) {
                    console.error(`Error notifying main process about ${type} whitelist:`, e);
                    return false;
                }
            } catch (e) {
                console.error(`Error synchronizing ${type} whitelist:`, e);
                return false;
            }
        }

        // This function is now a no-op since we don't automatically add domains to the whitelist
        function ensureSpecialDomainsWhitelisted() {
            console.log('Special domain handling disabled - domains must be explicitly added by the user');
            return false;
        }

        // Legacy function for backward compatibility
        function ensureDivineRevelationsWhitelisted() {
            return ensureSpecialDomainsWhitelisted();
        }

        // Run the debug function immediately
        debugWhitelist();

        // Ensure special domains are whitelisted
        ensureSpecialDomainsWhitelisted();

        // Improved save function with storage event triggering
        saveParentalControl.addEventListener('click', function() {
            // Save settings with minimal processing
            try {
                // Save checkbox states directly
                const oldBlockImages = localStorage.getItem('blockImages');
                const newBlockImages = blockImages.checked ? 'true' : 'false';
                localStorage.setItem('blockImages', newBlockImages);

                const oldBlockVideos = localStorage.getItem('blockVideos');
                const newBlockVideos = blockVideos.checked ? 'true' : 'false';
                localStorage.setItem('blockVideos', newBlockVideos);

                const oldBlockDownloads = localStorage.getItem('blockDownloads');
                const newBlockDownloads = blockDownloads.checked ? 'true' : 'false';
                localStorage.setItem('blockDownloads', newBlockDownloads);

                // Save image whitelist with minimal DOM operations
                var imageItems = [];
                var imageElements = document.querySelectorAll('#image-whitelist .whitelist-item span:not(:first-child)');
                for (var i = 0; i < imageElements.length; i++) {
                    imageItems.push(imageElements[i].textContent);
                }
                const oldImageWhitelist = localStorage.getItem('imageWhitelist');
                const newImageWhitelist = JSON.stringify(imageItems);
                localStorage.setItem('imageWhitelist', newImageWhitelist);

                // Save video whitelist with minimal DOM operations
                var videoItems = [];
                var videoElements = document.querySelectorAll('#video-whitelist .whitelist-item span:not(:first-child)');
                for (var i = 0; i < videoElements.length; i++) {
                    videoItems.push(videoElements[i].textContent);
                }
                const oldVideoWhitelist = localStorage.getItem('videoWhitelist');
                const newVideoWhitelist = JSON.stringify(videoItems);
                localStorage.setItem('videoWhitelist', newVideoWhitelist);

                // Trigger storage events for all changes
                if (oldBlockImages !== newBlockImages) {
                    window.dispatchEvent(new StorageEvent('storage', {
                        key: 'blockImages',
                        newValue: newBlockImages,
                        oldValue: oldBlockImages,
                        storageArea: localStorage
                    }));
                }

                if (oldImageWhitelist !== newImageWhitelist) {
                    window.dispatchEvent(new StorageEvent('storage', {
                        key: 'imageWhitelist',
                        newValue: newImageWhitelist,
                        oldValue: oldImageWhitelist,
                        storageArea: localStorage
                    }));

                    // Synchronize image whitelist with main process
                    console.log('Synchronizing image whitelist with main process after save');
                    syncWhitelistWithMainProcess('image');
                }

                if (oldBlockVideos !== newBlockVideos) {
                    window.dispatchEvent(new StorageEvent('storage', {
                        key: 'blockVideos',
                        newValue: newBlockVideos,
                        oldValue: oldBlockVideos,
                        storageArea: localStorage
                    }));
                }

                if (oldVideoWhitelist !== newVideoWhitelist) {
                    window.dispatchEvent(new StorageEvent('storage', {
                        key: 'videoWhitelist',
                        newValue: newVideoWhitelist,
                        oldValue: oldVideoWhitelist,
                        storageArea: localStorage
                    }));

                    // Synchronize video whitelist with main process
                    console.log('Synchronizing video whitelist with main process after save');
                    syncWhitelistWithMainProcess('video');
                }

                // Always synchronize settings with main process
                try {
                    const electron = require('electron');
                    const settings = {
                        blockImages: newBlockImages,
                        imageWhitelist: newImageWhitelist,
                        blockVideos: newBlockVideos,
                        videoWhitelist: newVideoWhitelist,
                        blockDownloads: newBlockDownloads
                    };
                    console.log('Sending settings to main process:', settings);
                    electron.ipcRenderer.send('save-settings', settings);
                } catch (e) {
                    console.error('Error sending settings to main process:', e);
                }

                // Show alert and restart the entire application
                alert('Settings saved! Click OK to restart the browser.');

                // Close modal
                settingsModal.style.display = 'none';

                // Use IPC to restart the entire Electron application
                try {
                    // Get electron from require
                    const electron = require('electron');
                    // Send restart message to main process
                    electron.ipcRenderer.send('restart-app');
                } catch (e) {
                    // Fallback to simple reload if IPC fails
                    console.error('Error restarting app:', e);
                    window.location.href = 'app.html';
                }
            } catch (e) {
                // If any error occurs, just save what we can and restart
                localStorage.setItem('blockImages', blockImages.checked ? 'true' : 'false');
                alert('Settings saved! Click OK to restart the browser.');

                // Use IPC to restart the entire Electron application
                try {
                    // Get electron from require
                    const electron = require('electron');
                    // Send restart message to main process
                    electron.ipcRenderer.send('restart-app');
                } catch (err) {
                    // Fallback to simple reload if IPC fails
                    console.error('Error restarting app:', err);
                    window.location.href = 'app.html';
                }
            }
        });

        // Apply parental control settings to webviews
        function applyParentalControlSettings() {
            // Image and video blocking logic has been moved to the main dom-ready handler
            // This function now only handles download blocking

            const blockDownloadsEnabled = localStorage.getItem('blockDownloads') === 'true';
            const downloadWhitelist = JSON.parse(localStorage.getItem('downloadWhitelist') || '[]');

            // Apply settings to all webviews
            tabs.forEach(tab => {
                if (tab.webview) {
                    // REMOVED dom-ready listener for image/video blocking from here.
                    // Image blocking logic is now consolidated in the main dom-ready listener.
                    // Video blocking notice is also in the main dom-ready.

                    // Handle downloads (this listener can remain here)
                    // Remove any previous listener to avoid duplicates if this function is called multiple times for the same webview
                    // A more robust way would be to use a named function and remove specific listener, or a flag.
                    // For now, this simplified removal might be okay if applyParentalControlSettings is called carefully.
                    // Consider if this function is called multiple times for the same webview, it would add multiple 'will-download' listeners.
                    // A better approach: attach 'will-download' once when webview is created.

                    // For simplicity, let's assume this function is effectively called once per webview for 'will-download' setup,
                    // or that multiple identical listeners are benign for 'will-download'.
                    // If issues arise, the 'will-download' listener attachment needs to be made idempotent.

                    tab.webview.addEventListener('will-download', (event, item) => {
                        if (blockDownloadsEnabled) {
                            const filePath = item.getFilename() || '';
                            const fileExt = filePath.split('.').pop().toLowerCase();

                            if (!isDomainWhitelisted_NEW(fileExt, downloadWhitelist)) { // Note: isDomainWhitelisted_NEW is for domains, might need adjustment for extensions
                                // Cancel the download
                                console.log(`DOWNLOAD BLOCKED: Extension '${fileExt}' not in whitelist. File: ${filePath}`);
                                item.cancel();

                                // Show notification
                                tab.webview.executeJavaScript('alert(\'Download blocked by parental controls. File type "' + fileExt + '" is not allowed.\');')
                                 .catch(e => console.error('Error showing download blocked alert:', e));
                            }
                        }
                    });
                }
            });
        }

        // Helper function to extract domain from URL
        function extractDomain(url) {
            try {
                const urlObj = new URL(url);
                return urlObj.hostname;
            } catch (e) {
                return '';
            }
        }

        // Helper function to normalize domain for whitelist comparison - ENHANCED VERSION
        function normalizeDomain(domain) {
            if (!domain) return '';

            try {
                // Convert to lowercase and trim
                let normalized = domain.toLowerCase().trim();

                // Log the original input
                console.log('Normalizing domain:', normalized);

                // Remove protocol (http://, https://)
                if (normalized.includes('://')) {
                    const beforeProtocol = normalized;
                    normalized = normalized.replace(/^https?:\/\//, '');
                    console.log('Removed protocol:', beforeProtocol, '→', normalized);
                }

                // Remove www. prefix
                if (normalized.startsWith('www.')) {
                    const beforeWww = normalized;
                    normalized = normalized.replace(/^www\./, '');
                    console.log('Removed www prefix:', beforeWww, '→', normalized);
                }

                // Remove trailing slash
                if (normalized.endsWith('/')) {
                    const beforeTrailingSlash = normalized;
                    normalized = normalized.replace(/\/$/, '');
                    console.log('Removed trailing slash:', beforeTrailingSlash, '→', normalized);
                }

                // Remove port number if present
                if (normalized.includes(':')) {
                    const beforePort = normalized;
                    normalized = normalized.replace(/:\d+$/, '');
                    console.log('Removed port number:', beforePort, '→', normalized);
                }

                // Remove path and query parameters
                if (normalized.includes('/')) {
                    const beforePath = normalized;
                    normalized = normalized.split('/')[0];
                    console.log('Removed path and query:', beforePath, '→', normalized);
                }

                // Handle special cases for common TLDs
                const commonTLDs = ['.com', '.org', '.net', '.edu', '.gov', '.info', '.io'];
                let hasTLD = false;

                // Check if the domain already has a common TLD
                for (const tld of commonTLDs) {
                    if (normalized.endsWith(tld)) {
                        hasTLD = true;
                        break;
                    }
                }

                // Ensure the domain has at least one dot (except for localhost)
                if (!normalized.includes('.') && normalized !== 'localhost') {
                    const beforeDot = normalized;
                    normalized = normalized + '.com';
                    console.log('Added .com to domain without dot:', beforeDot, '→', normalized);
                }

                // Special handling for domains with unusual TLDs
                if (normalized.includes('.') && !hasTLD) {
                    // Check if this might be a domain with an unusual TLD
                    const parts = normalized.split('.');
                    if (parts.length >= 2) {
                        const possibleTLD = '.' + parts[parts.length - 1];
                        console.log('Detected possible unusual TLD:', possibleTLD);
                    }
                }

                // Final normalized domain
                console.log('Final normalized domain:', domain, '→', normalized);
                return normalized;
            } catch (e) {
                console.error('Error normalizing domain:', e, 'for input:', domain);
                // Return a safe fallback
                const fallback = domain.toLowerCase().trim();
                console.log('Using fallback normalization:', fallback);
                return fallback;
            }
        }

        // Helper function to check if two domains are related using generic algorithmic approach
        function isRelatedDomainInApp(testDomain, whitelistedDomain) {
            // Extract the main domain identifier from both domains using multiple strategies
            const extractDomainIdentifiers = (domain) => {
                const parts = domain.split('.');
                if (parts.length < 2) return [domain];

                const identifiers = [];
                const secondLastPart = parts[parts.length - 2];

                // Strategy 1: Use the main domain part as-is
                identifiers.push(secondLastPart);

                // Strategy 2: Extract from hyphenated domains (e.g., "amazon-adsystem" -> "amazon")
                if (secondLastPart.includes('-')) {
                    const hyphenParts = secondLastPart.split('-');
                    // Add each meaningful part (length >= 3)
                    hyphenParts.forEach(part => {
                        if (part.length >= 3) {
                            identifiers.push(part);
                        }
                    });
                }

                // Strategy 3: Extract from camelCase or compound words (e.g., "googleapis" -> "google")
                // Look for common patterns where a shorter word might be embedded
                if (secondLastPart.length > 6) {
                    // Try to find embedded shorter domains (3-8 chars) at the beginning
                    for (let len = 3; len <= Math.min(8, secondLastPart.length - 2); len++) {
                        const prefix = secondLastPart.substring(0, len);
                        identifiers.push(prefix);
                    }
                }

                // Strategy 4: Handle numeric suffixes (e.g., "example2" -> "example")
                const withoutNumbers = secondLastPart.replace(/\d+$/, '');
                if (withoutNumbers !== secondLastPart && withoutNumbers.length >= 3) {
                    identifiers.push(withoutNumbers);
                }

                // Remove duplicates and filter meaningful identifiers
                return [...new Set(identifiers)].filter(id => id.length >= 3);
            };

            const testIdentifiers = extractDomainIdentifiers(testDomain);
            const whitelistedIdentifiers = extractDomainIdentifiers(whitelistedDomain);

            // Check for any matching identifiers
            for (const testId of testIdentifiers) {
                for (const whitelistedId of whitelistedIdentifiers) {
                    if (testId === whitelistedId && testId.length >= 3) {
                        console.log(`RELATED DOMAIN MATCH: '${testDomain}' (identifier: '${testId}') matches '${whitelistedDomain}' (identifier: '${whitelistedId}')`);
                        return true;
                    }
                }
            }

            // Additional check: if one domain contains the main part of another
            // This handles cases like "example.com" and "cdn.example-services.net"
            const testMainPart = testIdentifiers[0]; // Primary identifier
            const whitelistedMainPart = whitelistedIdentifiers[0]; // Primary identifier

            if (testMainPart && whitelistedMainPart && testMainPart.length >= 4 && whitelistedMainPart.length >= 4) {
                // Check if one is contained in the other (for compound domains)
                if (testMainPart.includes(whitelistedMainPart) || whitelistedMainPart.includes(testMainPart)) {
                    console.log(`RELATED DOMAIN MATCH (CONTAINS): '${testDomain}' (main: '${testMainPart}') related to '${whitelistedDomain}' (main: '${whitelistedMainPart}')`);
                    return true;
                }
            }

            return false;
        }

        // Enhanced isInWhitelist function with improved logging and matching
        function isDomainWhitelisted_NEW(domainToTest, whitelistArray) {
            // Validate inputs
            if (!domainToTest) {
                console.log('WHITELIST CHECK: Empty domain to test');
                return false;
            }

            if (!whitelistArray || !Array.isArray(whitelistArray) || whitelistArray.length === 0) {
                console.log('WHITELIST CHECK: Empty or invalid whitelist array');
                return false;
            }

            // Convert to string and normalize the domain we are testing
            const originalDomainToTest = String(domainToTest);
            const normalizedDomainToTest = normalizeDomain(originalDomainToTest.toLowerCase());

            console.log(`WHITELIST CHECK: Testing if '${originalDomainToTest}' (normalized: '${normalizedDomainToTest}') is in whitelist`);
            console.log('WHITELIST CHECK: Whitelist array:', whitelistArray);

            // Check each whitelist item
            for (const rawWhitelistedItem of whitelistArray) {
                if (!rawWhitelistedItem) {
                    console.log('WHITELIST CHECK: Skipping empty whitelist item');
                    continue;
                }

                // Normalize the item from the whitelist array
                const originalWhitelistedItem = String(rawWhitelistedItem);
                const normalizedWhitelistedItem = normalizeDomain(originalWhitelistedItem.toLowerCase());

                console.log(`WHITELIST CHECK: Comparing with whitelist item '${originalWhitelistedItem}' (normalized: '${normalizedWhitelistedItem}')`);

                // Check for exact match
                if (normalizedDomainToTest === normalizedWhitelistedItem) {
                    console.log(`WHITELIST MATCH (EXACT): '${normalizedDomainToTest}' exactly matches '${normalizedWhitelistedItem}'`);
                    return true;
                }

                // IMPROVED: Check for related domain patterns (e.g., amazon.com should match s.amazon-adsystem.com)
                if (isRelatedDomainInApp(normalizedDomainToTest, normalizedWhitelistedItem)) {
                    console.log(`WHITELIST MATCH (RELATED): '${normalizedDomainToTest}' is related to '${normalizedWhitelistedItem}'`);
                    return true;
                }

                // Check for subdomain match (e.g., mail.google.com matches google.com)
                if (normalizedDomainToTest.endsWith('.' + normalizedWhitelistedItem)) {
                    console.log(`WHITELIST MATCH (SUBDOMAIN): '${normalizedDomainToTest}' is a subdomain of '${normalizedWhitelistedItem}'`);
                    return true;
                }

                // Check for www variant match (e.g., www.example.com matches example.com)
                if (normalizedDomainToTest === 'www.' + normalizedWhitelistedItem ||
                    normalizedWhitelistedItem === 'www.' + normalizedDomainToTest) {
                    console.log(`WHITELIST MATCH (WWW VARIANT): '${normalizedDomainToTest}' is a www variant of '${normalizedWhitelistedItem}'`);
                    return true;
                }

                // Check for TLD variants (.com, .org, etc.) by comparing domain without TLD
                try {
                    // Extract domain without TLD for both domains
                    const domainParts = normalizedDomainToTest.split('.');
                    const whitelistParts = normalizedWhitelistedItem.split('.');

                    // If both have at least 2 parts (domain and TLD)
                    if (domainParts.length >= 2 && whitelistParts.length >= 2) {
                        // Compare the domain part (without TLD)
                        const domainWithoutTLD = domainParts.slice(0, -1).join('.');
                        const whitelistWithoutTLD = whitelistParts.slice(0, -1).join('.');

                        if (domainWithoutTLD === whitelistWithoutTLD) {
                            console.log(`WHITELIST MATCH (TLD VARIANT): '${normalizedDomainToTest}' matches '${normalizedWhitelistedItem}' ignoring TLD`);
                            return true;
                        }
                    }
                } catch (e) {
                    console.error('Error comparing domain TLDs:', e);
                }

                // Additional check: if the domain to test contains the whitelisted item (for partial matches)
                // This is a more lenient check that might help with some edge cases
                if (normalizedDomainToTest.includes(normalizedWhitelistedItem) ||
                    normalizedWhitelistedItem.includes(normalizedDomainToTest)) {
                    console.log(`WHITELIST MATCH (CONTAINS): '${normalizedDomainToTest}' contains or is contained in '${normalizedWhitelistedItem}'`);
                    console.log(`WARNING: This is a partial match and might cause unexpected behavior`);
                    return true;
                }
            }

            // No match found
            console.log(`WHITELIST NO MATCH: '${normalizedDomainToTest}' (Original: ${originalDomainToTest}) not in whitelist`);
            console.log('WHITELIST NO MATCH: Normalized whitelist items:', whitelistArray.map(item => normalizeDomain(String(item).toLowerCase())));
            return false;
        }

        // Initialize the global whitelist // This comment and the call below can be removed if updateGlobalWhitelist is gone
        // updateGlobalWhitelist(); // REMOVE THIS CALL

        // Apply parental control settings to webviews
        // Function to update the global whitelist // THIS BLOCK IS TO BE DELETED
        // function updateGlobalWhitelist() { // START DELETE (approx line 3636)
        //    try {
        //        // Get whitelist from localStorage
        //        const storedWhitelist = localStorage.getItem('imageWhitelist');
        //        if (storedWhitelist) {
        //            globalImageWhitelist = JSON.parse(storedWhitelist);
        //        } else {
        //            globalImageWhitelist = [];
        //        }
        //
        //        // Convert all items to lowercase for case-insensitive comparison
        //        globalImageWhitelist = globalImageWhitelist.map(item => item.toLowerCase().trim());
        //
        //        console.log('GLOBAL WHITELIST UPDATED:', globalImageWhitelist);
        //    } catch (e) {
        //        console.error('Error updating global whitelist:', e);
        //        globalImageWhitelist = [];
        //    }
        // } // END DELETE (approx line 3648)

        // HARDCODED whitelist check function - direct string comparison with global whitelist // THIS BLOCK IS TO BE DELETED
        // function isInWhitelist(value, whitelist) { // START DELETE (approx line 3660)
            // Always use the global whitelist instead of the passed whitelist
        //    updateGlobalWhitelist(); // Ensure we have the latest whitelist
        //
            // Basic validation
        //    if (!value) {
        //        console.log('WHITELIST: Empty domain value');
        //        return false;
        //    }
        //
        //    if (globalImageWhitelist.length === 0) {
        //        console.log('WHITELIST: Empty whitelist');
        //        return false;
        //    }
        //
            // Convert to lowercase for case-insensitive comparison
        //    const domain = value.toLowerCase().trim();
        //
        //    console.log('WHITELIST: Checking if', domain, 'is in GLOBAL whitelist:', globalImageWhitelist);
        //
            // Direct string comparison - no fancy logic
        //    for (const whitelistItem of globalImageWhitelist) {
                // Skip empty items
        //        if (!whitelistItem) continue;
        //
        //        console.log('WHITELIST: Comparing with', whitelistItem);
        //
                // Direct contains check - simplest possible approach
        //        if (domain.includes(whitelistItem) || whitelistItem.includes(domain)) {
        //            console.log('WHITELIST: ✓ Match found! Domain', domain, 'matches whitelist item', whitelistItem);
        //            return true;
        //        }
        //    }
        //
        //    console.log('WHITELIST: ✗ No match found for', domain);
        //    return false;
        // } // END DELETE (approx line 3685)

        // Load parental control settings on startup
        loadParentalControlSettings();

        // Update current domain when navigation happens
        function updateCurrentDomain(url) {
            try {
                const urlObj = new URL(url);
                currentDomain = urlObj.hostname;
                updateTimerDisplay();
            } catch (e) {
                console.error('Failed to parse URL:', e);
                currentDomain = '';
            }
        }

        // Update webview event listeners to track domain changes
        function addTimeTrackingToWebview(webview) {
            webview.addEventListener('did-navigate', (event) => {
                updateCurrentDomain(event.url);
            });

            webview.addEventListener('did-navigate-in-page', (event) => {
                updateCurrentDomain(event.url);
            });
        }

        // Store original createTab function before modifying it
        const originalCreateTabFn = createTab;

        // Replace createTab with enhanced version that adds time tracking and parental controls
        createTab = function(url = 'https://www.google.com') {
            // Call the original function
            const tabId = originalCreateTabFn(url);

            // Find the tab we just created
            const tabInfo = tabs.find(t => t.id === tabId); // Renamed to tabInfo to avoid conflict

            // Add time tracking to the webview
            if (tabInfo && tabInfo.webview) {
                addTimeTrackingToWebview(tabInfo.webview);

                // Apply parental control settings (which now mainly sets up 'will-download') to the new tab's webview
                // This needs to be called such that it targets the *specific new webview*
                // The current applyParentalControlSettings iterates all 'tabs'.
                // It's better to pass the specific webview to a modified applyParentalControlSettings.
                // For now, calling the general one. If it causes issues (e.g. re-attaching listeners to old tabs), this needs refinement.
                // The iteration within applyParentalControlSettings will find this new tab.
                applyParentalControlSettings(); // This will iterate all tabs including the new one.
            }

            return tabId;
        };

        // Function to reset password state if needed
        function resetPasswordState() {
            // Clear any pending password callbacks
            currentPasswordCallback = null;

            // Hide password modal if it's showing
            if (passwordModal) {
                passwordModal.style.display = 'none';
            }

            // Make sure password field is empty
            if (settingPassword) {
                settingPassword.value = '';
            }

            console.log('Password state reset, protection status:', isPasswordProtected());
        }

        // Initialize directly without DOMContentLoaded to fix white screen
        console.log('Initializing app...');

        // Create initial tab with Google search
        try {
            createTab('https://www.google.com');
            console.log('Initial tab created with Google search');
        } catch (e) {
            console.error('Error creating initial tab:', e);
        }

        // Then load other features
        try {
            resetPasswordState();
            loadBookmarks();

            // Initialize time limits with a delay to prevent password prompt on startup
            setTimeout(() => {
                try {
                    initTimeLimits();
                    console.log('Time limits initialized');
                } catch (e) {
                    console.error('Error initializing time limits:', e);
                }
            }, 1000);
        } catch (e) {
            console.error('Error during initialization:', e);
        }

        // Download notification elements
        const downloadNotification = document.getElementById('download-notification');
        const downloadFilename = document.getElementById('download-filename');
        const downloadStatus = document.getElementById('download-status');
        const downloadProgressBar = document.getElementById('download-progress-bar');
        const closeDownloadNotification = document.getElementById('close-download-notification');

        // Close download notification
        closeDownloadNotification.addEventListener('click', () => {
            downloadNotification.style.display = 'none';
        });

        // No test notification on startup

        // Handle download events from main process
        const { ipcRenderer } = require('electron');

        // Track active downloads
        const activeDownloads = {};

        // Handle download started
        ipcRenderer.on('download-started', (event, data) => {
            console.log('Download started:', data);

            // Store download info
            activeDownloads[data.id] = {
                filename: data.filename,
                state: 'downloading', // Set state to 'downloading' immediately
                progress: 0
            };

            // Make sure any previous notification is hidden
            downloadNotification.style.display = 'none';

            // Show notification with 0% progress
            downloadFilename.textContent = data.filename;
            downloadStatus.textContent = 'Starting download...';
            downloadStatus.style.color = 'white';
            downloadStatus.style.fontWeight = 'normal';
            downloadProgressBar.style.width = '0%';
            downloadProgressBar.style.backgroundColor = '#4CAF50';

            // Add pulsing animation for indeterminate progress
            downloadProgressBar.style.backgroundImage = 'linear-gradient(45deg, rgba(255,255,255,.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,.15) 50%, rgba(255,255,255,.15) 75%, transparent 75%, transparent)';
            downloadProgressBar.style.backgroundSize = '40px 40px';
            downloadProgressBar.style.animation = 'progress-bar-stripes 2s linear infinite';

            // Add animation keyframes if not already added
            if (!document.getElementById('progress-bar-animation')) {
                const style = document.createElement('style');
                style.id = 'progress-bar-animation';
                style.textContent = `
                    @keyframes progress-bar-stripes {
                        from { background-position: 40px 0; }
                        to { background-position: 0 0; }
                    }
                `;
                document.head.appendChild(style);
            }

            // Show the notification
            downloadNotification.style.display = 'block';

            // Show close button
            const closeBtn = document.getElementById('close-download-notification');
            if (closeBtn) {
                closeBtn.style.display = 'block';
            }

            // Add to download history immediately
            addToDownloadHistory({
                filename: data.filename,
                status: 'Downloading',
                state: 'downloading',
                progress: 0,
                time: new Date().toLocaleTimeString()
            });

            // Update the history UI
            updateDownloadHistoryUI();
        });

        // Handle download blocked
        ipcRenderer.on('download-blocked', (event, data) => {
            console.log('Download blocked:', data);

            // Show notification
            downloadFilename.textContent = data.filename;
            const blockMessage = `Download blocked: ${data.extension} files are not allowed`;
            downloadStatus.textContent = blockMessage;
            downloadStatus.style.color = '#ff5252';
            downloadStatus.style.fontWeight = 'normal';
            downloadProgressBar.style.width = '100%';
            downloadProgressBar.style.backgroundColor = '#ff5252';
            // Reset animation
            downloadProgressBar.style.backgroundImage = 'none';
            downloadProgressBar.style.animation = 'none';
            downloadProgressBar.innerHTML = ''; // Clear any text
            downloadNotification.style.display = 'block';

            // Add to download history with a slight delay to ensure UI is updated
            setTimeout(() => {
                addToDownloadHistory({
                    filename: data.filename,
                    status: blockMessage,
                    state: 'blocked',
                    time: new Date().toLocaleTimeString()
                });

                // Force update the history UI
                updateDownloadHistoryUI();

                // If the history panel is visible, make sure it stays visible
                if (downloadHistoryPanel.style.display === 'block') {
                    downloadHistoryPanel.style.display = 'block';
                }
            }, 100);

            // Hide after 5 seconds
            setTimeout(() => {
                downloadNotification.style.display = 'none';
            }, 5000);
        });

        // Handle download progress
        ipcRenderer.on('download-updated', (event, data) => {
            console.log('Download update:', data);

            // Check if this is a tracked download
            if (!activeDownloads[data.id]) {
                console.log('Ignoring update for unknown download:', data.id);
                return;
            }

            // Update download info
            activeDownloads[data.id].state = data.state;
            if (data.progress) {
                activeDownloads[data.id].progress = data.progress;
            }

            // Show notification
            downloadFilename.textContent = data.filename;
            downloadNotification.style.display = 'block';

            // Reset notification appearance
            downloadNotification.style.boxShadow = '0 4px 20px rgba(0,0,0,0.4)';

            // Restore close button
            const closeBtn = document.getElementById('close-download-notification');
            if (closeBtn) {
                closeBtn.style.display = 'block';
            }

            // Reset font weight
            downloadStatus.style.fontWeight = 'normal';

            if (data.state === 'progressing') {
                if (data.progress >= 0) {
                    // Known progress - show percentage
                    const progressText = `${data.progress}%`;
                    downloadStatus.textContent = `Downloading: ${progressText}`;
                    downloadStatus.style.color = 'white';
                    downloadProgressBar.style.width = `${data.progress}%`;
                    downloadProgressBar.style.backgroundColor = '#4CAF50';

                    // Reset animation
                    downloadProgressBar.style.backgroundImage = 'none';
                    downloadProgressBar.style.animation = 'none';

                    // Add percentage text directly on the progress bar
                    downloadProgressBar.innerHTML = `<span style="position: absolute; left: 50%; transform: translateX(-50%); color: white; text-shadow: 0 0 2px rgba(0,0,0,0.7); font-weight: bold; font-size: 12px;">${progressText}</span>`;

                    // Update or add to download history with current progress
                    const existingDownloadIndex = downloadHistory.findIndex(d =>
                        d.filename === data.filename && d.state === 'downloading');

                    if (existingDownloadIndex >= 0) {
                        // Update existing download
                        downloadHistory[existingDownloadIndex].status = `Downloading`;
                        downloadHistory[existingDownloadIndex].progress = data.progress;
                        downloadHistory[existingDownloadIndex].time = new Date().toLocaleTimeString();
                    } else {
                        // Add new download to history
                        addToDownloadHistory({
                            filename: data.filename,
                            status: `Downloading`,
                            state: 'downloading',
                            progress: data.progress,
                            time: new Date().toLocaleTimeString()
                        });
                    }

                    // Always update the history UI to show real-time progress
                    updateDownloadHistoryUI();
                } else {
                    // Indeterminate progress - show animated progress bar
                    downloadStatus.textContent = 'Downloading...';
                    downloadStatus.style.color = 'white';
                    downloadProgressBar.style.width = '100%';
                    downloadProgressBar.style.backgroundColor = '#4CAF50';
                    downloadProgressBar.innerHTML = ''; // Clear any text

                    // Add animation
                    downloadProgressBar.style.backgroundImage = 'linear-gradient(45deg, rgba(255,255,255,.15) 25%, transparent 25%, transparent 50%, rgba(255,255,255,.15) 50%, rgba(255,255,255,.15) 75%, transparent 75%, transparent)';
                    downloadProgressBar.style.backgroundSize = '40px 40px';
                    downloadProgressBar.style.animation = 'progress-bar-stripes 2s linear infinite';

                    // Add animation keyframes if not already added
                    if (!document.getElementById('progress-bar-animation')) {
                        const style = document.createElement('style');
                        style.id = 'progress-bar-animation';
                        style.textContent = `
                            @keyframes progress-bar-stripes {
                                from { background-position: 40px 0; }
                                to { background-position: 0 0; }
                            }
                        `;
                        document.head.appendChild(style);
                    }

                    // Update or add to download history with indeterminate progress
                    const existingDownloadIndex = downloadHistory.findIndex(d =>
                        d.filename === data.filename && d.state === 'downloading');

                    if (existingDownloadIndex >= 0) {
                        // Update existing download
                        downloadHistory[existingDownloadIndex].status = 'Downloading';
                        downloadHistory[existingDownloadIndex].progress = -1;
                        downloadHistory[existingDownloadIndex].time = new Date().toLocaleTimeString();
                    } else {
                        // Add new download to history
                        addToDownloadHistory({
                            filename: data.filename,
                            status: 'Downloading',
                            state: 'downloading',
                            progress: -1,
                            time: new Date().toLocaleTimeString()
                        });
                    }

                    // Always update the history UI to show real-time progress
                    updateDownloadHistoryUI();
                }
            } else if (data.state === 'interrupted') {
                downloadStatus.textContent = 'Download interrupted';
                downloadStatus.style.color = '#ff9800';
                downloadProgressBar.style.width = '100%';
                downloadProgressBar.style.backgroundColor = '#ff9800';
                downloadProgressBar.style.backgroundImage = 'none';
                downloadProgressBar.style.animation = 'none';
                downloadProgressBar.innerHTML = ''; // Clear any text
            } else if (data.state === 'paused') {
                downloadStatus.textContent = 'Download paused';
                downloadStatus.style.color = '#ff9800';
                downloadProgressBar.style.width = '100%';
                downloadProgressBar.style.backgroundColor = '#ff9800';
                downloadProgressBar.style.backgroundImage = 'none';
                downloadProgressBar.style.animation = 'none';
                downloadProgressBar.innerHTML = ''; // Clear any text
            }
        });

        // Handle download completed
        ipcRenderer.on('download-completed', (event, data) => {
            console.log('Download completed:', data);

            // Check if this is a tracked download
            if (!activeDownloads[data.id]) {
                console.log('Ignoring completion for unknown download:', data.id);
                return;
            }

            // Update download info
            activeDownloads[data.id].state = 'completed';
            activeDownloads[data.id].path = data.path;

            // Show notification
            downloadFilename.textContent = data.filename;
            downloadStatus.textContent = 'Download completed';
            downloadStatus.style.color = '#4CAF50';
            downloadProgressBar.style.width = '100%';
            downloadProgressBar.style.backgroundColor = '#4CAF50';
            // Reset animation
            downloadProgressBar.style.backgroundImage = 'none';
            downloadProgressBar.style.animation = 'none';
            // Show 100% text
            downloadProgressBar.innerHTML = '<span style="position: absolute; left: 50%; transform: translateX(-50%); color: white; text-shadow: 0 0 2px rgba(0,0,0,0.7); font-weight: bold; font-size: 12px;">100%</span>';
            downloadNotification.style.display = 'block';

            // Add to download history with a slight delay to ensure UI is updated
            setTimeout(() => {
                addToDownloadHistory({
                    filename: data.filename,
                    status: 'Download completed',
                    state: 'completed',
                    path: data.path,
                    time: new Date().toLocaleTimeString()
                });

                // Force update the history UI
                updateDownloadHistoryUI();

                // If the history panel is visible, make sure it stays visible
                if (downloadHistoryPanel.style.display === 'block') {
                    downloadHistoryPanel.style.display = 'block';
                }
            }, 100);

            // Hide after 5 seconds
            setTimeout(() => {
                downloadNotification.style.display = 'none';
                // Clean up download info
                delete activeDownloads[data.id];
            }, 5000);
        });

        // Handle download failed
        ipcRenderer.on('download-failed', (event, data) => {
            console.log('Download failed:', data);

            // Check if this is a tracked download
            if (!activeDownloads[data.id]) {
                console.log('Ignoring failure for unknown download:', data.id);
                return;
            }

            // Update download info
            activeDownloads[data.id].state = 'failed';

            // Show notification
            downloadFilename.textContent = data.filename;
            const failureMessage = `Download failed: ${data.state}`;
            downloadStatus.textContent = failureMessage;
            downloadStatus.style.color = '#ff5252';
            downloadProgressBar.style.width = '100%';
            downloadProgressBar.style.backgroundColor = '#ff5252';
            // Reset animation
            downloadProgressBar.style.backgroundImage = 'none';
            downloadProgressBar.style.animation = 'none';
            downloadProgressBar.innerHTML = ''; // Clear any text
            downloadNotification.style.display = 'block';

            // Add to download history with a slight delay to ensure UI is updated
            setTimeout(() => {
                addToDownloadHistory({
                    filename: data.filename,
                    status: failureMessage,
                    state: 'failed',
                    time: new Date().toLocaleTimeString()
                });

                // Force update the history UI
                updateDownloadHistoryUI();

                // If the history panel is visible, make sure it stays visible
                if (downloadHistoryPanel.style.display === 'block') {
                    downloadHistoryPanel.style.display = 'block';
                }
            }, 100);

            // Hide after 5 seconds
            setTimeout(() => {
                downloadNotification.style.display = 'none';
                // Clean up download info
                delete activeDownloads[data.id];
            }, 5000);
        });

        // Update download whitelist when saving parental control settings
        saveParentalControl.addEventListener('click', function() {
            // Get download whitelist items
            try {
                var downloadItems = [];
                var downloadElements = document.querySelectorAll('#download-whitelist .whitelist-item span:not(:first-child)');
                for (var i = 0; i < downloadElements.length; i++) {
                    downloadItems.push(downloadElements[i].textContent);
                }

                // Send to main process
                ipcRenderer.send('update-download-whitelist', downloadItems);

                // Send block downloads setting
                ipcRenderer.send('set-block-downloads', blockDownloads.checked);
            } catch (e) {
                console.error('Error updating download whitelist:', e);
            }
        });

        // Download history management
        const downloadHistory = [];
        const downloadHistoryPanel = document.getElementById('download-history-panel');
        const downloadHistoryList = document.getElementById('download-history-list');
        const clearDownloadHistory = document.getElementById('clear-download-history');

        // Function to add a download to history
        function addToDownloadHistory(download) {
            console.log('Adding download to history:', download);

            // Check if this download already exists in history
            const existingIndex = downloadHistory.findIndex(d =>
                d.filename === download.filename &&
                (d.state === 'downloading' || download.state === 'completed' && d.state === 'downloading')
            );

            if (existingIndex >= 0) {
                // Update existing download
                console.log('Updating existing download in history at index:', existingIndex);
                downloadHistory[existingIndex] = download;
            } else {
                // Add new download to beginning of array
                downloadHistory.unshift(download);
            }

            // Update the UI
            updateDownloadHistoryUI();

            // Log the current history
            console.log('Current download history:', downloadHistory);
        }

        // Function to update the download history UI - simplified for better performance
        function updateDownloadHistoryUI() {
            // Clear the list
            downloadHistoryList.innerHTML = '';

            if (downloadHistory.length === 0) {
                downloadHistoryList.innerHTML = '<div style="padding: 20px; text-align: center; color: #888; font-size: 14px;">No downloads yet</div>';
                return;
            }

            // Add each download to the list - using a simplified approach
            downloadHistory.forEach((download, index) => {
                const item = document.createElement('div');
                item.style.padding = '10px';
                item.style.borderBottom = index < downloadHistory.length - 1 ? '1px solid #333' : 'none';
                item.style.display = 'flex';
                item.style.alignItems = 'center';

                // Simple icon selection
                let icon = '📄';
                let statusColor = '#aaa';

                if (download.state === 'completed') {
                    icon = '✅';
                    statusColor = '#4CAF50';
                } else if (download.state === 'failed' || download.state === 'blocked') {
                    icon = '❌';
                    statusColor = '#ff5252';
                } else if (download.state === 'downloading') {
                    icon = '⏬';
                    statusColor = '#2196F3';
                }

                // Create a very simple progress display for downloads in progress
                let progressHtml = '';
                if (download.state === 'downloading' && download.progress >= 0) {
                    progressHtml = `
                        <div style="display: flex; align-items: center; margin-top: 4px;">
                            <div style="width: 100px; height: 4px; background: #333; margin-right: 5px;">
                                <div style="height: 100%; width: ${download.progress}%; background: #4CAF50;"></div>
                            </div>
                            <span style="color: #4CAF50; font-size: 11px; font-weight: bold;">${download.progress}%</span>
                        </div>
                    `;
                }

                // Create a very simple HTML structure
                item.innerHTML = `
                    <div style="margin-right: 10px;">${icon}</div>
                    <div style="flex-grow: 1; overflow: hidden;">
                        <div style="font-weight: bold; font-size: 13px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">${download.filename}</div>
                        <div style="font-size: 9px; color: ${statusColor};">${download.status}</div>
                        ${progressHtml}
                    </div>
                `;

                // Add to list
                downloadHistoryList.appendChild(item);
            });
        }

        // Clear download history
        clearDownloadHistory.addEventListener('click', function() {
            downloadHistory.length = 0;
            updateDownloadHistoryUI();
        });

        // Initialize download history panel
        downloadHistoryPanel.style.display = 'none'; // Ensure it's hidden initially

        // Clear any existing history
        downloadHistory.length = 0;

        // Toggle download history panel
        const testDownloadBtn = document.getElementById('test-download');
        testDownloadBtn.addEventListener('click', function(e) {
            e.preventDefault(); // Prevent any default action
            console.log('Download button clicked, toggling history panel');

            if (downloadHistoryPanel.style.display === 'none') {
                downloadHistoryPanel.style.display = 'block';
                // Update the UI in case new downloads happened
                updateDownloadHistoryUI();
            } else {
                downloadHistoryPanel.style.display = 'none';
            }
        });

        // Dark Mode Functionality
        // Initialize dark mode from localStorage
        function initDarkMode() {
            const darkModeEnabled = localStorage.getItem('darkMode') === 'true';
            console.log('Initializing dark mode, enabled:', darkModeEnabled);

            if (darkModeEnabled) {
                document.body.classList.add('dark-mode');
                if (darkModeToggle) darkModeToggle.checked = true;
            } else {
                document.body.classList.remove('dark-mode');
                if (darkModeToggle) darkModeToggle.checked = false;
            }
        }

        // Apply dark mode to webviews
        function applyDarkModeToWebviews() {
            const darkModeEnabled = localStorage.getItem('darkMode') === 'true';
            console.log('Applying dark mode to webviews, enabled:', darkModeEnabled);

            // Apply to all webviews
            tabs.forEach(tab => {
                if (tab.webview && tab.webview.getWebContents) {
                    try {
                        const webContents = tab.webview.getWebContents();
                        if (webContents) {
                            if (darkModeEnabled) {
                                // Apply dark mode CSS
                                tab.webview.insertCSS(`
                                    html, body {
                                        background-color: #1e1e1e !important;
                                        color: #e0e0e0 !important;
                                    }

                                    a {
                                        color: #4285f4 !important;
                                    }

                                    input, textarea, select {
                                        background-color: #333 !important;
                                        color: #e0e0e0 !important;
                                        border-color: #444 !important;
                                    }
                                `).catch(e => console.error('Error injecting dark mode CSS:', e));
                            } else {
                                // Reload the page to remove dark mode
                                tab.webview.reload();
                            }
                        }
                    } catch (e) {
                        console.error('Error applying dark mode to webview:', e);
                    }
                }
            });
        }

        // Save dark mode setting
        function saveDarkModeSetting() {
            const darkModeEnabled = darkModeToggle.checked;
            console.log('Saving dark mode setting:', darkModeEnabled);

            localStorage.setItem('darkMode', darkModeEnabled);

            if (darkModeEnabled) {
                document.body.classList.add('dark-mode');
            } else {
                document.body.classList.remove('dark-mode');
            }

            // Apply to webviews
            applyDarkModeToWebviews();
        }

        // Initialize dark mode
        initDarkMode();

        // Add event listener to save general settings
        if (saveGeneralSettings) {
            saveGeneralSettings.addEventListener('click', function() {
                console.log('Saving general settings');

                // Save dark mode setting
                saveDarkModeSetting();

                // Save other general settings
                localStorage.setItem('showBookmarksToolbar', document.getElementById('setting-show-bookmarks-toolbar').checked);
                localStorage.setItem('startFullscreen', document.getElementById('setting-start-fullscreen').checked);
                localStorage.setItem('showTimer', document.getElementById('setting-show-timer').checked);
                localStorage.setItem('showRemainingTime', document.getElementById('setting-show-remaining-time').checked);

                // Show confirmation and restart
                alert('Settings saved! Click OK to restart the browser.');

                // Close modal
                settingsModal.style.display = 'none';

                // Use IPC to restart the entire Electron application
                try {
                    // Get electron from require
                    const electron = require('electron');
                    // Send restart message to main process
                    electron.ipcRenderer.send('restart-app');
                } catch (e) {
                    // Fallback to simple reload if IPC fails
                    console.error('Error restarting app:', e);
                    window.location.href = 'app.html';
                }
            });
        }

        // Window controls
        document.getElementById('titlebar-minimize').addEventListener('click', () => {
            const { getCurrentWindow } = require('@electron/remote');
            getCurrentWindow().minimize();
        });

        document.getElementById('titlebar-maximize').addEventListener('click', () => {
            const { getCurrentWindow } = require('@electron/remote');
            const win = getCurrentWindow();
            if (win.isMaximized()) {
                win.unmaximize();
            } else {
                win.maximize();
            }
        });

        document.getElementById('titlebar-close').addEventListener('click', () => {
            const { getCurrentWindow } = require('@electron/remote');
            getCurrentWindow().close();
        });

        // Update maximize button icon when window state changes
        const { getCurrentWindow } = require('@electron/remote');
        const win = getCurrentWindow();
        win.on('maximize', () => {
            document.getElementById('titlebar-maximize').textContent = '❐';
        });
        win.on('unmaximize', () => {
            document.getElementById('titlebar-maximize').textContent = '□';
        });

        // Menu functionality
        document.getElementById('new-tab-btn').addEventListener('click', () => {
            const { getCurrentWindow } = require('@electron/remote');
            const win = getCurrentWindow();
            win.webContents.send('new-tab');
        });

        document.getElementById('new-window').addEventListener('click', () => {
            const { getCurrentWindow } = require('@electron/remote');
            const win = getCurrentWindow();
            win.webContents.send('new-window');
        });

        document.getElementById('exit').addEventListener('click', () => {
            const { getCurrentWindow } = require('@electron/remote');
            getCurrentWindow().close();
        });

        // Edit menu functionality
        document.getElementById('undo').addEventListener('click', () => {
            const { getCurrentWindow } = require('@electron/remote');
            const win = getCurrentWindow();
            win.webContents.undo();
        });

        document.getElementById('redo').addEventListener('click', () => {
            const { getCurrentWindow } = require('@electron/remote');
            const win = getCurrentWindow();
            win.webContents.redo();
        });

        document.getElementById('cut').addEventListener('click', () => {
            const { getCurrentWindow } = require('@electron/remote');
            const win = getCurrentWindow();
            win.webContents.cut();
        });

        document.getElementById('copy').addEventListener('click', () => {
            const { getCurrentWindow } = require('@electron/remote');
            const win = getCurrentWindow();
            win.webContents.copy();
        });

        document.getElementById('paste').addEventListener('click', () => {
            const { getCurrentWindow } = require('@electron/remote');
            const win = getCurrentWindow();
            win.webContents.paste();
        });

        // View menu functionality
        document.getElementById('zoom-in').addEventListener('click', () => {
            const { getCurrentWindow } = require('@electron/remote');
            const win = getCurrentWindow();
            const currentZoom = win.webContents.getZoomFactor();
            win.webContents.setZoomFactor(currentZoom + 0.1);
        });

        document.getElementById('zoom-out').addEventListener('click', () => {
            const { getCurrentWindow } = require('@electron/remote');
            const win = getCurrentWindow();
            const currentZoom = win.webContents.getZoomFactor();
            win.webContents.setZoomFactor(currentZoom - 0.1);
        });

        document.getElementById('zoom-reset').addEventListener('click', () => {
            const { getCurrentWindow } = require('@electron/remote');
            const win = getCurrentWindow();
            win.webContents.setZoomFactor(1.0);
        });

        // Reset the time limits password
        localStorage.removeItem('password');

        // In the Password Protection section, add an inline error message element below the password fields
        let passwordErrorMsg = document.getElementById('password-error-msg');
        if (!passwordErrorMsg) {
            passwordErrorMsg = document.createElement('div');
            passwordErrorMsg.id = 'password-error-msg';
            passwordErrorMsg.style.color = '#d32f2f';
            passwordErrorMsg.style.fontWeight = 'bold';
            passwordErrorMsg.style.margin = '6px 0 0 0';
            passwordErrorMsg.style.display = 'none';
            // Insert after the password row
            const pwdRow = settingPassword.parentNode.parentNode;
            pwdRow.parentNode.insertBefore(passwordErrorMsg, pwdRow.nextSibling);
        }

        // Websites & Cookies tab elements
        const saveCookiesCheckbox = document.getElementById('setting-save-cookies');
        const cookieWhitelistDomain = document.getElementById('cookie-whitelist-domain');
        const addCookieWhitelist = document.getElementById('add-cookie-whitelist');
        const cookieWhitelistContainer = document.getElementById('cookie-whitelist');
        const saveWebsitesCookies = document.getElementById('save-websites-cookies');

        // Advanced tab elements
        const blocklistDomains = document.getElementById('blocklist-domains');
        const blocklistUrls = document.getElementById('blocklist-urls');
        const blocklistKeywordsInput = document.getElementById('blocklist-keywords-input');
        const addBlocklistKeyword = document.getElementById('add-blocklist-keyword');
        const blocklistKeywordsList = document.getElementById('blocklist-keywords-list');
        const saveAdvancedSettings = document.getElementById('save-advanced-settings');
        const redirectUrlInput = document.getElementById('redirect-url');

        // Load advanced settings
        function loadAdvancedSettings() {
            console.log('Loading advanced settings');

            // Try to get settings from localStorage
            let settings = {};
            try {
                const settingsJson = localStorage.getItem('settings');
                if (settingsJson) {
                    settings = JSON.parse(settingsJson);
                    console.log('Loaded settings from localStorage:', settings);
                }
            } catch (e) {
                console.error('Error parsing settings from localStorage:', e);
            }

            // Save cookies
            const saveCookies = settings.saveCookies === 'true' || localStorage.getItem('saveCookies') === 'true';
            if (saveCookiesCheckbox) saveCookiesCheckbox.checked = saveCookies;

            // Blocklists - try to get from settings object first, then fallback to individual localStorage items
            const blockDomains = settings.blockDomains || localStorage.getItem('blockDomains') || '';
            console.log('Loaded block domains:', blockDomains);

            const blockUrls = settings.blockUrls || localStorage.getItem('blockUrls') || '';
            console.log('Loaded block URLs:', blockUrls);

            const blockKeywords = settings.blockKeywords || localStorage.getItem('blockKeywords') || '';
            console.log('Loaded block keywords:', blockKeywords);

            // Redirect URL
            const redirectUrl = settings.redirectUrl || localStorage.getItem('redirectUrl') || '';
            console.log('Loaded redirect URL:', redirectUrl);

            // Set input values
            if (redirectUrlInput) redirectUrlInput.value = redirectUrl;

            // Clear existing lists
            if (blocklistDomainsList) blocklistDomainsList.innerHTML = '';
            if (blocklistUrlsList) blocklistUrlsList.innerHTML = '';
            if (blocklistKeywordsList) blocklistKeywordsList.innerHTML = '';

            // Populate domain list
            if (blockDomains) {
                blockDomains.split('\n').forEach(domain => {
                    if (domain.trim()) {
                        addDomainToList(domain.trim());
                    }
                });
            }

            // Populate URL list
            if (blockUrls) {
                blockUrls.split('\n').forEach(url => {
                    if (url.trim()) {
                        addUrlToList(url.trim());
                    }
                });
            }

            // Populate keyword list
            if (blockKeywords) {
                blockKeywords.split('\n').forEach(keyword => {
                    if (keyword.trim()) {
                        addKeywordToList(keyword.trim());
                    }
                });
            }

            console.log('Advanced settings loaded successfully');
        }

        // Save advanced settings - DIRECT APPROACH
        function saveAdvanced() {
            console.log('SAVE ADVANCED: Starting to save advanced settings');

            try {
                // Gather block domains from visible list
                const domains = Array.from(document.querySelectorAll('#blocklist-domains-list .blocklist-domain-item span'))
                    .map(e => e.textContent.trim())
                    .filter(Boolean);

                // Gather block URLs from visible list
                const urls = Array.from(document.querySelectorAll('#blocklist-urls-list .blocklist-url-item span'))
                    .map(e => e.textContent.trim())
                    .filter(Boolean);

                // Gather block keywords from visible list
                const keywords = Array.from(document.querySelectorAll('#blocklist-keywords-list .blocklist-keyword-item span'))
                    .map(e => e.textContent.trim())
                    .filter(Boolean);

                // Get redirect URL
                const redirectUrlValue = redirectUrlInput ? redirectUrlInput.value.trim() : '';

                // Get save cookies setting
                const saveCookiesValue = saveCookiesCheckbox ? saveCookiesCheckbox.checked : false;

                console.log('SAVE ADVANCED: Collected settings:');
                console.log('- Domains:', domains);
                console.log('- URLs:', urls);
                console.log('- Keywords:', keywords);
                console.log('- Redirect URL:', redirectUrlValue);
                console.log('- Save Cookies:', saveCookiesValue);

                // Create settings object
                const settings = {
                    saveCookies: saveCookiesValue ? 'true' : 'false',
                    blockDomains: domains.join('\n'),
                    blockUrls: urls.join('\n'),
                    blockKeywords: keywords.join('\n'),
                    redirectUrl: redirectUrlValue
                };

                // Save to localStorage for persistence
                localStorage.setItem('blockDomains', domains.join('\n'));
                localStorage.setItem('blockUrls', urls.join('\n'));
                localStorage.setItem('blockKeywords', keywords.join('\n'));
                localStorage.setItem('redirectUrl', redirectUrlValue);
                localStorage.setItem('saveCookies', saveCookiesValue ? 'true' : 'false');

                // Save to settings object in localStorage
                localStorage.setItem('settings', JSON.stringify(settings));

                console.log('SAVE ADVANCED: Settings saved to localStorage');

                // Send to main process using Electron IPC
                const { ipcRenderer } = require('electron');

                console.log('SAVE ADVANCED: Sending settings to main process:', settings);
                ipcRenderer.send('save-settings', settings);

                // Show confirmation and restart
                alert('Advanced settings saved! The browser will now restart to apply your changes.');

                // Close settings modal
                if (settingsModal) {
                    settingsModal.style.display = 'none';
                }

                // Restart the app
                console.log('SAVE ADVANCED: Restarting app to apply changes');
                ipcRenderer.send('restart-app');

            } catch (error) {
                console.error('SAVE ADVANCED: Error saving settings:', error);
                alert('Error saving settings: ' + error.message + '\nPlease try again.');
            }
        }
        if (saveAdvancedSettings) {
            saveAdvancedSettings.addEventListener('click', saveAdvanced);
        }
        // Enter key support for redirect URL
        if (redirectUrlInput) {
            redirectUrlInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    saveAdvanced();
                }
            });
        }

        // Helper to add keyword to list
        function addKeywordToList(keyword) {
            if (!keyword) return;
            // Prevent duplicates
            const existing = Array.from(blocklistKeywordsList.querySelectorAll('.blocklist-keyword-item span')).map(e => e.textContent.trim().toLowerCase());
            if (existing.includes(keyword.toLowerCase())) return;
            const item = document.createElement('div');
            item.className = 'blocklist-keyword-item';
            item.style.display = 'flex';
            item.style.justifyContent = 'space-between';
            item.style.alignItems = 'center';
            item.style.padding = '8px 12px';
            item.style.margin = '4px 0';
            item.style.background = '#f5f5f5';
            item.style.borderRadius = '6px';
            item.style.fontWeight = '500';
            // Keyword text
            const span = document.createElement('span');
            span.textContent = keyword;
            item.appendChild(span);
            // Remove button
            const removeBtn = document.createElement('button');
            removeBtn.innerHTML = '&times;';
            removeBtn.title = 'Remove keyword';
            removeBtn.style.padding = '2px 10px';
            removeBtn.style.background = '#f44336';
            removeBtn.style.color = 'white';
            removeBtn.style.border = 'none';
            removeBtn.style.borderRadius = '4px';
            removeBtn.style.cursor = 'pointer';
            removeBtn.style.fontSize = '16px';
            removeBtn.style.fontWeight = 'bold';
            removeBtn.addEventListener('click', () => {
                item.remove();
            });
            item.appendChild(removeBtn);
            blocklistKeywordsList.appendChild(item);
        }

        // Add keyword on button click
        addBlocklistKeyword.addEventListener('click', () => {
            const keyword = blocklistKeywordsInput.value.trim();
            if (keyword) {
                addKeywordToList(keyword);
                blocklistKeywordsInput.value = '';
            }
        });

        // Add keyword on Enter
        blocklistKeywordsInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const keyword = blocklistKeywordsInput.value.trim();
                if (keyword) {
                    addKeywordToList(keyword);
                    blocklistKeywordsInput.value = '';
                }
            }
        });

        // Load keywords list from localStorage
        function loadBlocklistKeywords() {
            if (!blocklistKeywordsList) {
                console.error('blocklistKeywordsList element not found');
                return;
            }

            console.log('Loading blocklist keywords');
            blocklistKeywordsList.innerHTML = '';

            // Try to get keywords from settings object first
            let keywords = [];
            try {
                const settingsJson = localStorage.getItem('settings');
                if (settingsJson) {
                    const settings = JSON.parse(settingsJson);
                    if (settings.blockKeywords) {
                        keywords = settings.blockKeywords.split('\n').map(x => x.trim()).filter(Boolean);
                        console.log('Loaded keywords from settings object:', keywords);
                    }
                }
            } catch (e) {
                console.error('Error parsing settings for keywords:', e);
            }

            // If no keywords found in settings, try the legacy storage
            if (keywords.length === 0) {
                keywords = (localStorage.getItem('blockKeywords') || '').split('\n').map(x => x.trim()).filter(Boolean);
                console.log('Loaded keywords from legacy storage:', keywords);
            }

            // Add each keyword to the list
            keywords.forEach(keyword => {
                if (keyword) {
                    console.log('Adding keyword to list:', keyword);
                    addKeywordToList(keyword);
                }
            });

            console.log('Finished loading blocklist keywords');
        }

        // Save keywords list to localStorage
        function saveBlocklistKeywords() {
            const keywords = Array.from(blocklistKeywordsList.querySelectorAll('.blocklist-keyword-item span')).map(e => e.textContent.trim()).filter(Boolean);
            localStorage.setItem('blockKeywords', keywords.join('\n'));
        }

        // Update loadAdvancedSettings and saveAdvanced to use new list UI
        const oldLoadAdvancedSettings = loadAdvancedSettings;
        loadAdvancedSettings = function() {
            if (oldLoadAdvancedSettings) oldLoadAdvancedSettings();
            loadBlocklistKeywords();
        };
        const oldSaveAdvanced = saveAdvanced;
        saveAdvanced = function() {
            saveBlocklistKeywords();
            if (oldSaveAdvanced) oldSaveAdvanced();
        };

        // Advanced tab elements
        const blocklistDomainsInput = document.getElementById('blocklist-domains-input');
        const addBlocklistDomain = document.getElementById('add-blocklist-domain');
        const blocklistDomainsList = document.getElementById('blocklist-domains-list');
        const blocklistUrlsInput = document.getElementById('blocklist-urls-input');
        const addBlocklistUrl = document.getElementById('add-blocklist-url');
        const blocklistUrlsList = document.getElementById('blocklist-urls-list');

        // Helper to add domain to list
        function addDomainToList(domain) {
            if (!domain) return;
            const existing = Array.from(blocklistDomainsList.querySelectorAll('.blocklist-domain-item span')).map(e => e.textContent.trim().toLowerCase());
            if (existing.includes(domain.toLowerCase())) return;
            const item = document.createElement('div');
            item.className = 'blocklist-domain-item';
            item.style.display = 'flex';
            item.style.justifyContent = 'space-between';
            item.style.alignItems = 'center';
            item.style.padding = '8px 12px';
            item.style.margin = '4px 0';
            item.style.background = '#f5f5f5';
            item.style.borderRadius = '6px';
            item.style.fontWeight = '500';
            const span = document.createElement('span');
            span.textContent = domain;
            item.appendChild(span);
            const removeBtn = document.createElement('button');
            removeBtn.innerHTML = '&times;';
            removeBtn.title = 'Remove domain';
            removeBtn.style.padding = '2px 10px';
            removeBtn.style.background = '#f44336';
            removeBtn.style.color = 'white';
            removeBtn.style.border = 'none';
            removeBtn.style.borderRadius = '4px';
            removeBtn.style.cursor = 'pointer';
            removeBtn.style.fontSize = '16px';
            removeBtn.style.fontWeight = 'bold';
            removeBtn.addEventListener('click', () => { item.remove(); });
            item.appendChild(removeBtn);
            blocklistDomainsList.appendChild(item);
        }

        // Helper to add URL to list
        function addUrlToList(url) {
            if (!url) return;
            const existing = Array.from(blocklistUrlsList.querySelectorAll('.blocklist-url-item span')).map(e => e.textContent.trim().toLowerCase());
            if (existing.includes(url.toLowerCase())) return;
            const item = document.createElement('div');
            item.className = 'blocklist-url-item';
            item.style.display = 'flex';
            item.style.justifyContent = 'space-between';
            item.style.alignItems = 'center';
            item.style.padding = '8px 12px';
            item.style.margin = '4px 0';
            item.style.background = '#f5f5f5';
            item.style.borderRadius = '6px';
            item.style.fontWeight = '500';
            const span = document.createElement('span');
            span.textContent = url;
            item.appendChild(span);
            const removeBtn = document.createElement('button');
            removeBtn.innerHTML = '&times;';
            removeBtn.title = 'Remove URL';
            removeBtn.style.padding = '2px 10px';
            removeBtn.style.background = '#f44336';
            removeBtn.style.color = 'white';
            removeBtn.style.border = 'none';
            removeBtn.style.borderRadius = '4px';
            removeBtn.style.cursor = 'pointer';
            removeBtn.style.fontSize = '16px';
            removeBtn.style.fontWeight = 'bold';
            removeBtn.addEventListener('click', () => { item.remove(); });
            item.appendChild(removeBtn);
            blocklistUrlsList.appendChild(item);
        }

        // Add domain on button click
        addBlocklistDomain.addEventListener('click', () => {
            const domain = blocklistDomainsInput.value.trim();
            if (domain) {
                addDomainToList(domain);
                blocklistDomainsInput.value = '';
            }
        });

        // Add domain on Enter
        blocklistDomainsInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const domain = blocklistDomainsInput.value.trim();
                if (domain) {
                    addDomainToList(domain);
                    blocklistDomainsInput.value = '';
                }
            }
        });

        // Add URL on button click
        addBlocklistUrl.addEventListener('click', () => {
            const url = blocklistUrlsInput.value.trim();
            if (url) {
                addUrlToList(url);
                blocklistUrlsInput.value = '';
            }
        });

        // Add URL on Enter
        blocklistUrlsInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const url = blocklistUrlsInput.value.trim();
                if (url) {
                    addUrlToList(url);
                    blocklistUrlsInput.value = '';
                }
            }
        });

        // Cookie Whitelist Functions

        // Helper to add domain to cookie whitelist
        function addDomainToCookieWhitelist(domain) {
            if (!domain) return;

            // Prevent duplicates
            const existing = Array.from(cookieWhitelistContainer.querySelectorAll('.whitelist-item span:not(:first-child)')).map(e => e.textContent.trim().toLowerCase());
            if (existing.includes(domain.toLowerCase())) return;

            // Create whitelist item
            const item = document.createElement('div');
            item.className = 'whitelist-item';
            item.style.display = 'flex';
            item.style.justifyContent = 'space-between';
            item.style.alignItems = 'center';
            item.style.padding = '8px 12px';
            item.style.margin = '4px 0';
            item.style.background = '#f5f5f5';
            item.style.borderRadius = '6px';

            // Domain icon and text
            const domainSpan = document.createElement('span');
            domainSpan.innerHTML = '🌐 ';
            domainSpan.style.marginRight = '5px';

            const textSpan = document.createElement('span');
            textSpan.textContent = domain;
            textSpan.style.fontWeight = '500';

            // Remove button
            const removeBtn = document.createElement('button');
            removeBtn.innerHTML = '&times;';
            removeBtn.title = 'Remove from whitelist';
            removeBtn.style.padding = '2px 10px';
            removeBtn.style.background = '#f44336';
            removeBtn.style.color = 'white';
            removeBtn.style.border = 'none';
            removeBtn.style.borderRadius = '4px';
            removeBtn.style.cursor = 'pointer';
            removeBtn.style.fontSize = '16px';
            removeBtn.style.fontWeight = 'bold';
            removeBtn.addEventListener('click', () => {
                item.remove();
            });

            // Assemble the item
            item.appendChild(domainSpan);
            item.appendChild(textSpan);
            item.appendChild(removeBtn);

            // Add to container
            cookieWhitelistContainer.appendChild(item);
        }

        // Add domain to cookie whitelist on button click
        addCookieWhitelist.addEventListener('click', () => {
            const domain = cookieWhitelistDomain.value.trim();
            if (domain) {
                addDomainToCookieWhitelist(domain);
                cookieWhitelistDomain.value = '';
            }
        });

        // Add domain to cookie whitelist on Enter
        cookieWhitelistDomain.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                const domain = cookieWhitelistDomain.value.trim();
                if (domain) {
                    addDomainToCookieWhitelist(domain);
                    cookieWhitelistDomain.value = '';
                }
            }
        });

        // Load cookie whitelist from localStorage
        function loadCookieWhitelist() {
            if (!cookieWhitelistContainer) {
                console.error('cookieWhitelistContainer element not found');
                return;
            }

            console.log('Loading cookie whitelist');
            cookieWhitelistContainer.innerHTML = '';

            // Get whitelist from localStorage
            let whitelist = [];
            try {
                const whitelistJson = localStorage.getItem('cookieWhitelist');
                if (whitelistJson) {
                    whitelist = JSON.parse(whitelistJson);
                    console.log('Loaded cookie whitelist:', whitelist);
                }
            } catch (e) {
                console.error('Error parsing cookie whitelist:', e);
            }

            // Add each domain to the whitelist UI
            whitelist.forEach(domain => {
                if (domain) {
                    addDomainToCookieWhitelist(domain);
                }
            });
        }

        // Save cookie whitelist to localStorage
        function saveCookieWhitelist() {
            const domains = Array.from(cookieWhitelistContainer.querySelectorAll('.whitelist-item span:not(:first-child)')).map(e => e.textContent.trim());
            localStorage.setItem('cookieWhitelist', JSON.stringify(domains));

            // Also save to settings object
            try {
                const settingsJson = localStorage.getItem('settings');
                let settings = settingsJson ? JSON.parse(settingsJson) : {};
                settings.cookieWhitelist = domains;
                localStorage.setItem('settings', JSON.stringify(settings));
            } catch (e) {
                console.error('Error saving cookie whitelist to settings:', e);
            }

            return domains;
        }

        // Save websites & cookies settings
        saveWebsitesCookies.addEventListener('click', function() {
            console.log('Saving websites & cookies settings');

            // Save cookie whitelist
            const whitelist = saveCookieWhitelist();
            console.log('Saved cookie whitelist:', whitelist);

            // Save save cookies setting
            const saveCookiesValue = saveCookiesCheckbox.checked;
            localStorage.setItem('saveCookies', saveCookiesValue ? 'true' : 'false');

            // Update settings object
            try {
                const settingsJson = localStorage.getItem('settings');
                let settings = settingsJson ? JSON.parse(settingsJson) : {};
                settings.saveCookies = saveCookiesValue ? 'true' : 'false';
                settings.cookieWhitelist = whitelist;
                localStorage.setItem('settings', JSON.stringify(settings));

                // Send to main process
                const { ipcRenderer } = require('electron');
                ipcRenderer.send('save-settings', settings);
            } catch (e) {
                console.error('Error saving websites & cookies settings:', e);
            }

            // Show confirmation
            alert('Websites & Cookies settings saved!');

            // Close settings modal
            settingsModal.style.display = 'none';
        });

        // Load websites & cookies settings
        function loadWebsitesCookiesSettings() {
            console.log('Loading websites & cookies settings');

            // Load save cookies setting
            let saveCookies = false;
            try {
                const settingsJson = localStorage.getItem('settings');
                if (settingsJson) {
                    const settings = JSON.parse(settingsJson);
                    saveCookies = settings.saveCookies === 'true';
                } else {
                    saveCookies = localStorage.getItem('saveCookies') === 'true';
                }
            } catch (e) {
                console.error('Error loading save cookies setting:', e);
                saveCookies = localStorage.getItem('saveCookies') === 'true';
            }

            // Set checkbox
            if (saveCookiesCheckbox) {
                saveCookiesCheckbox.checked = saveCookies;
            }

            // Load cookie whitelist
            loadCookieWhitelist();
        }

        // Get the base domain (e.g., example.com from sub.example.com)
        function getBaseDomain(domain) {
            if (!domain) return '';

            try {
                const parts = domain.split('.');
                if (parts.length <= 2) return domain; // Already a base domain

                // Get the last two parts (e.g., example.com from sub.example.com)
                return parts.slice(-2).join('.');
            } catch (e) {
                console.error('Error getting base domain:', e);
                return domain;
            }
        }

        // Global whitelist variable that will be used throughout the application
        // let globalImageWhitelist = []; // Commented out

        // Function to update the global whitelist
        function updateGlobalWhitelist() {
            // Function body emptied - OLD FUNCTION, SHOULD NOT BE USED.
            console.warn('OLD updateGlobalWhitelist() was called. This should not happen.');
        }

        // Initialize the global whitelist
        // updateGlobalWhitelist(); // Commented out

        // Whitelist check function - redirects to the improved implementation
        function isInWhitelist(value, whitelist) {
            // This function exists for backward compatibility
            // It ensures all code paths use the same whitelist checking logic
            return isDomainWhitelisted_NEW(value, whitelist);
        }

        // Initialize video blocking settings if they don't exist (preserve existing settings)
        if (!localStorage.getItem('blockVideos')) {
            localStorage.setItem('blockVideos', 'false');
        }
        if (!localStorage.getItem('videoWhitelist')) {
            localStorage.setItem('videoWhitelist', '[]');
        }
        console.log('Video blocking settings initialized (functionality disabled, UI preserved).');

        // Load parental control settings on startup
        loadParentalControlSettings();

        // Image blocking is handled in the main DOM-ready event handler above
        // Video blocking functionality is disabled (UI and settings preserved)
    </script>
</body>
</html>