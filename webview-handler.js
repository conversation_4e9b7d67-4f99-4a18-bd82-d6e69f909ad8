const { shell } = require('electron');

// Helper function for improved whitelist checking
function isInWhitelistImproved(domain, whitelist) {
    if (!domain || !whitelist || !whitelist.length) return false;

    const normalizedDomain = domain.toLowerCase().trim();

    return whitelist.some(whitelistDomain => {
        const normalizedWhitelistDomain = whitelistDomain.toLowerCase().trim();

        // Exact match
        if (normalizedDomain === normalizedWhitelistDomain) return true;

        // Subdomain match
        if (normalizedDomain.endsWith('.' + normalizedWhitelistDomain)) return true;

        // Related domain check (Amazon-style)
        if (isRelatedDomainSimple(normalizedDomain, normalizedWhitelistDomain)) return true;

        return false;
    });
}

// Generic algorithmic related domain check for webview handler
function isRelatedDomainSimple(testDomain, whitelistedDomain) {
    // Extract domain identifiers using simplified algorithmic approach
    const extractSimpleIdentifiers = (domain) => {
        const parts = domain.split('.');
        if (parts.length < 2) return [domain];

        const identifiers = [];
        const secondLastPart = parts[parts.length - 2];

        // Add the main part
        identifiers.push(secondLastPart);

        // Extract from hyphenated domains
        if (secondLastPart.includes('-')) {
            const hyphenParts = secondLastPart.split('-');
            hyphenParts.forEach(part => {
                if (part.length >= 3) {
                    identifiers.push(part);
                }
            });
        }

        // Extract prefixes from compound words
        if (secondLastPart.length > 6) {
            for (let len = 3; len <= Math.min(8, secondLastPart.length - 2); len++) {
                identifiers.push(secondLastPart.substring(0, len));
            }
        }

        return [...new Set(identifiers)].filter(id => id.length >= 3);
    };

    const testIdentifiers = extractSimpleIdentifiers(testDomain);
    const whitelistedIdentifiers = extractSimpleIdentifiers(whitelistedDomain);

    // Check for matching identifiers
    for (const testId of testIdentifiers) {
        for (const whitelistedId of whitelistedIdentifiers) {
            if (testId === whitelistedId && testId.length >= 3) {
                return true;
            }
        }
    }

    return false;
}

// Webview event handling
function setupWebview(webview) {
    webview.addEventListener('dom-ready', () => {
        try {
            const url = webview.getURL();
            const domain = new URL(url).hostname;

            // Clear all retry counters on successful navigation
            if (webview._webviewRetryCount) {
                console.log('Clearing webview retry counters after successful navigation');
                webview._webviewRetryCount = {};
            }
            if (webview._abortRetryCount) {
                console.log('Clearing ERR_ABORTED retry counters after successful navigation');
                webview._abortRetryCount = {};
            }

            // Get settings from store
            const settings = store.get('settings') || {};
            const blockImages = settings.blockImages === 'true';
            const imageWhitelist = settings.imageWhitelist ? JSON.parse(settings.imageWhitelist) : [];

            // Check if current domain is in whitelist using improved logic
            const isWhitelisted = isInWhitelistImproved(domain, imageWhitelist);

            if (blockImages && !isWhitelisted) {
                webview.insertCSS('img, picture, source { display: none !important; }');
            }
        } catch (err) {
            console.error('Error in dom-ready handler:', err);
        }
    });

    webview.addEventListener('did-navigate', (event) => {
        console.log('Navigation occurred:', event.url);

        // Update webview src to match the actual URL after navigation
        // This helps prevent issues with redirects
        try {
            if (webview.src !== event.url) {
                console.log('Updating webview src after navigation:', event.url);
                // Don't use loadURL here as it would cause another navigation
                // Just update the internal reference
                webview._actualUrl = event.url;
            }
        } catch (err) {
            console.warn('Error updating webview URL reference:', err);
        }
    });

    webview.addEventListener('did-start-navigation', (event) => {
        console.log('Navigation started:', event.url, 'isInPlace:', event.isInPlace, 'isMainFrame:', event.isMainFrame);

        // Track navigation state to prevent conflicts
        if (event.isMainFrame) {
            webview._isNavigating = true;
            webview._navigationStartTime = Date.now();
        }
    });

    webview.addEventListener('did-redirect-navigation', (event) => {
        console.log('Navigation redirected:', event.url, 'isInPlace:', event.isInPlace, 'isMainFrame:', event.isMainFrame);

        // Track redirects to help with error recovery
        if (event.isMainFrame) {
            webview._lastRedirectUrl = event.url;
            webview._isNavigating = true; // Still navigating after redirect
        }
    });

    webview.addEventListener('did-finish-load', (event) => {
        console.log('Navigation finished for:', webview.getURL());

        // Clear navigation state
        webview._isNavigating = false;
        webview._navigationStartTime = null;
    });

    webview.addEventListener('did-stop-loading', (event) => {
        console.log('Navigation stopped for:', webview.getURL());

        // Clear navigation state
        webview._isNavigating = false;
        webview._navigationStartTime = null;
    });

    webview.addEventListener('page-title-updated', (event) => {
        console.log('Page title updated:', event.title);
    });

    webview.addEventListener('did-fail-load', (event) => {
        console.error(`Webview failed to load: ${event.validatedURL}, Error: ${event.errorCode} (${event.errorDescription}), MainFrame: ${event.isMainFrame}`);

        // Handle specific error codes
        if (event.errorCode === -2) { // ERR_FAILED
            console.log('ERR_FAILED detected in webview, this might be due to network issues or site restrictions');

            // For main frame failures, we could show a custom error page
            if (event.isMainFrame) {
                console.log('Main frame failed to load in webview');

                // Optionally inject a custom error page
                setTimeout(() => {
                    try {
                        if (webview && webview.src) {
                            webview.executeJavaScript(`
                                document.body.innerHTML = \`
                                    <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                                        <h2>Failed to Load Page</h2>
                                        <p>The page could not be loaded due to a network error.</p>
                                        <p>URL: ${event.validatedURL}</p>
                                        <p>Error: ${event.errorDescription} (${event.errorCode})</p>
                                        <button onclick="window.location.reload()" style="padding: 10px 20px; font-size: 16px; margin: 10px;">
                                            Try Again
                                        </button>
                                    </div>
                                \`;
                            `).catch(err => console.error('Error injecting error page:', err));
                        }
                    } catch (err) {
                        console.error('Error handling failed load:', err);
                    }
                }, 1000);
            }
        } else if (event.errorCode === -3) { // ERR_ABORTED
            console.log('Navigation was aborted in webview for:', event.validatedURL, 'MainFrame:', event.isMainFrame);

            // Don't retry subframe aborts at all
            if (!event.isMainFrame) {
                console.log('ERR_ABORTED in subframe in webview, treating as intentional');
                return;
            }

            // For main frame ERR_ABORTED, implement intelligent retry logic
            const failedUrl = event.validatedURL;
            const currentUrl = webview.getURL();

            // Initialize retry counter if not exists
            if (!webview._abortRetryCount) {
                webview._abortRetryCount = {};
            }

            const retryKey = failedUrl || 'unknown';
            const currentRetries = webview._abortRetryCount[retryKey] || 0;

            // Only retry once for ERR_ABORTED to prevent loops
            if (currentRetries >= 1) {
                console.log('ERR_ABORTED retry limit reached for:', retryKey);
                return;
            }

            try {
                const failedUrlObj = new URL(failedUrl);
                const currentUrlObj = currentUrl ? new URL(currentUrl) : null;

                // Check if this looks like a legitimate redirect failure
                const isLikelyRedirectFailure = (
                    !currentUrl || currentUrl === 'about:blank' || // Initial navigation
                    (currentUrlObj && failedUrlObj.hostname !== currentUrlObj.hostname) || // Cross-domain redirect
                    failedUrlObj.search.includes('gatewayAdapt') || // AliExpress gateway redirect
                    failedUrlObj.pathname.includes('/w/') // AliExpress product page
                );

                if (isLikelyRedirectFailure) {
                    console.log('ERR_ABORTED appears to be a failed redirect, attempting retry for:', failedUrl);

                    // Increment retry counter
                    webview._abortRetryCount[retryKey] = currentRetries + 1;

                    // Retry with a delay to allow any ongoing navigation to complete
                    setTimeout(() => {
                        try {
                            if (webview && document.body.contains(webview)) {
                                console.log('Retrying ERR_ABORTED navigation to:', failedUrl);
                                webview.loadURL(failedUrl);
                            }
                        } catch (retryError) {
                            console.error('Error during ERR_ABORTED retry:', retryError);
                        }
                    }, 2000); // 2 second delay
                    return;
                }
            } catch (urlErr) {
                console.log('ERR_ABORTED with invalid URL, not retrying:', urlErr);
                return;
            }

            console.log('ERR_ABORTED appears to be intentional navigation cancellation, not retrying');
        } else if (event.errorCode === -105) { // ERR_NAME_NOT_RESOLVED
            console.log('DNS resolution failed in webview for:', event.validatedURL);
        }
    });

    webview.addEventListener('render-process-gone', (event) => {
        console.log('Webview render process gone:', event.reason, 'exitCode:', event.exitCode);

        // DISABLE AUTOMATIC CRASH RECOVERY TO PREVENT GUEST_VIEW_MANAGER_CALL ERRORS
        // Automatic reloading after crashes can cause navigation conflicts
        // Let the user manually refresh if needed

        if (event.reason === 'crashed') {
            console.log('Webview crashed - automatic recovery disabled to prevent GUEST_VIEW_MANAGER_CALL errors');
            console.log('User can manually refresh the page if needed');
        } else if (event.reason === 'oom-killed') {
            console.log('Webview was killed due to out of memory - automatic recovery disabled');
            console.log('User can manually refresh the page if needed');
        } else {
            console.log('Webview process ended normally, reason:', event.reason);
        }

        // No automatic recovery attempts to prevent navigation conflicts
    });

    webview.addEventListener('unresponsive', () => {
        console.log('Webview became unresponsive - automatic recovery disabled to prevent navigation conflicts');
        console.log('User can manually refresh if the page remains unresponsive');

        // No automatic recovery to prevent GUEST_VIEW_MANAGER_CALL errors
    });

    webview.addEventListener('responsive', () => {
        console.log('Webview became responsive again');

        // No cleanup needed since we don't set timeouts anymore
    });

    // Set up webview preferences optimized for complex websites like AliExpress
    try {
        // Enhanced webview configuration for better compatibility
        webview.setAttribute('webpreferences',
            'images=true, javascript=true, webSecurity=false, contextIsolation=false, ' +
            'allowRunningInsecureContent=true, experimentalFeatures=true, ' +
            'backgroundThrottling=false, offscreen=false'
        );

        // Use a shared partition for better compatibility with complex sites
        webview.setAttribute('partition', 'persist:main');

        webview.setAttribute('allowpopups', 'true');
        webview.setAttribute('preload', './preload.js');

        // Enhanced stability and compatibility attributes
        webview.setAttribute('nodeintegration', 'false');
        webview.setAttribute('nodeintegrationinsubframes', 'false');
        webview.setAttribute('enableremotemodule', 'false');
        webview.setAttribute('disablewebsecurity', 'true'); // Enable for complex sites
        webview.setAttribute('allowRunningInsecureContent', 'true'); // Enable for compatibility
        webview.setAttribute('experimentalfeatures', 'true');

        // Additional attributes for better navigation handling
        webview.setAttribute('httpreferrer', 'https://www.google.com/');
        webview.setAttribute('blinkfeatures', 'CSSCustomProperties,CSSGridLayout');

        console.log('Webview preferences set with enhanced compatibility for complex websites');
    } catch (err) {
        console.error('Error setting webview preferences:', err);
    }

    // Add error handling for webview lifecycle
    webview.addEventListener('dom-ready', () => {
        console.log('Webview DOM ready for:', webview.getURL());
    });

    webview.addEventListener('did-start-loading', () => {
        console.log('Webview started loading:', webview.getURL());
    });

    webview.addEventListener('did-stop-loading', () => {
        console.log('Webview stopped loading:', webview.getURL());
    });

    // Enhanced navigation handling for complex websites
    // Special handling for sites that have complex redirect patterns

    webview.addEventListener('will-navigate', (event) => {
        const url = event.url;
        console.log('Webview will-navigate to:', url);

        // Enhanced handling for complex websites like AliExpress
        if (url && url.includes('aliexpress.com')) {
            console.log('AliExpress URL detected:', url);

            try {
                const urlObj = new URL(url);

                // Always prevent complex AliExpress navigations and handle them specially
                if (urlObj.search.includes('spm=') || urlObj.search.includes('gatewayAdapt') || urlObj.pathname.includes('/w/')) {
                    console.log('Complex AliExpress navigation detected, preventing and handling specially');

                    event.preventDefault();

                    // Navigate to the base AliExpress domain first, then let it redirect naturally
                    const baseDomain = `${urlObj.protocol}//${urlObj.hostname}/`;

                    setTimeout(() => {
                        try {
                            if (webview && document.body.contains(webview)) {
                                console.log('Navigating to AliExpress base domain first:', baseDomain);
                                webview.loadURL(baseDomain);

                                // After base domain loads, try the original URL
                                setTimeout(() => {
                                    try {
                                        if (webview && document.body.contains(webview)) {
                                            console.log('Now navigating to original AliExpress URL:', url);
                                            webview.loadURL(url);
                                        }
                                    } catch (secondNavError) {
                                        console.error('Error in second navigation:', secondNavError);
                                    }
                                }, 3000); // Wait 3 seconds for base domain to load
                            }
                        } catch (navError) {
                            console.error('Error navigating to base domain:', navError);
                        }
                    }, 100);

                    return;
                }
            } catch (urlError) {
                console.error('Error parsing AliExpress URL:', urlError);
            }
        }
    });
}

// Function to create a new webview with improved error handling
function createWebview(url, tabId) {
    // Special handling for AliExpress - use iframe instead of webview to avoid ERR_ABORTED
    if (url && url.includes('aliexpress.com')) {
        console.log('AliExpress URL detected, using iframe fallback to avoid ERR_ABORTED errors');
        return createIframeFallback(url, tabId);
    }

    try {
        console.log(`Creating new webview for URL: ${url}, TabID: ${tabId}`);

        const webview = document.createElement('webview');
        const webviewContainer = document.getElementById('webview-container');
        const tabsContainer = document.getElementById('tabs');

        if (!webviewContainer || !tabsContainer) {
            console.error('Required containers not found for webview creation');
            return null;
        }

        const tab = document.createElement('div');
        tab.className = 'tab';
        tab.textContent = 'New Tab';
        tab.dataset.tabId = tabId;

        // Set up webview before setting src to prevent race conditions
        setupWebview(webview);

        // Set style first
        webview.style.display = 'none';
        webview.style.width = '100%';
        webview.style.height = '100%';
        webview.style.flex = '1';

        // Add unique ID for tracking
        webview.id = `webview-${tabId}`;

        // Set a modern Chrome user agent to improve compatibility with complex websites
        webview.setAttribute('useragent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36 Edg/121.0.0.0');

        // Add to container before setting src to ensure proper initialization
        webviewContainer.appendChild(webview);

        // Add to tabs array
        tabs.push({ id: tabId, webview: webview, element: tab });
        tabsContainer.appendChild(tab);

        // Set src last to trigger loading after everything is set up
        // Use a longer delay to ensure webview is fully initialized
        setTimeout(() => {
            try {
                if (webview && document.body.contains(webview)) {
                    console.log(`Setting webview src to: ${url} (after initialization delay)`);

                    // Add an additional check to ensure webview is ready
                    if (webview.getWebContents) {
                        console.log('Webview appears to be fully initialized');
                    }

                    // Enhanced URL handling for complex websites
                    let finalUrl = url;

                    // Special handling for AliExpress URLs to prevent ERR_ABORTED
                    if (url && url.includes('aliexpress.com')) {
                        try {
                            const urlObj = new URL(url);

                            // For complex AliExpress URLs, start with base domain
                            if (urlObj.search.includes('spm=') || urlObj.search.includes('gatewayAdapt') || urlObj.pathname.includes('/w/')) {
                                console.log('Complex AliExpress URL detected for initial load, starting with base domain');

                                // Start with base domain, then navigate to full URL after loading
                                finalUrl = `${urlObj.protocol}//${urlObj.hostname}/`;

                                // Set up delayed navigation to the original URL
                                setTimeout(() => {
                                    try {
                                        if (webview && document.body.contains(webview)) {
                                            console.log('Now navigating to original AliExpress URL after base load:', url);
                                            webview.loadURL(url);
                                        }
                                    } catch (delayedNavError) {
                                        console.error('Error in delayed AliExpress navigation:', delayedNavError);
                                    }
                                }, 4000); // Wait 4 seconds for base domain to fully load

                                console.log('Starting with base domain:', finalUrl);
                            }
                        } catch (urlError) {
                            console.error('Error handling AliExpress URL:', urlError);
                            finalUrl = url; // Fall back to original URL
                        }
                    }

                    webview.src = finalUrl;
                } else {
                    console.error('Webview was removed before src could be set');
                }
            } catch (srcError) {
                console.error('Error setting webview src:', srcError);

                // Retry disabled to prevent GUEST_VIEW_MANAGER_CALL errors
                console.log('Webview src retry disabled to prevent navigation conflicts');
                // setTimeout(() => {
                //     try {
                //         if (webview && document.body.contains(webview)) {
                //             console.log('Retrying webview src setting after error');
                //             webview.src = url;
                //         }
                //     } catch (retryError) {
                //         console.error('Retry setting webview src also failed:', retryError);
                //     }
                // }, 500); // DISABLED
            }
        }, 500); // Increased delay from 100ms to 500ms

        console.log(`Webview created successfully for TabID: ${tabId}`);
        return webview;

    } catch (error) {
        console.error('Error creating webview:', error);
        return null;
    }
}

// Function to create an iframe fallback for problematic sites
function createIframeFallback(url, tabId) {
    try {
        console.log(`Creating iframe fallback for URL: ${url}, TabID: ${tabId}`);

        const webviewContainer = document.getElementById('webview-container');
        const tabsContainer = document.getElementById('tabs-container');

        if (!webviewContainer || !tabsContainer) {
            console.error('Required containers not found for iframe fallback');
            return null;
        }

        // Create tab element
        const tab = document.createElement('div');
        tab.className = 'tab';
        tab.id = `tab-${tabId}`;
        tab.innerHTML = `
            <span class="tab-title">Loading...</span>
            <span class="tab-close" onclick="closeTab('${tabId}')">×</span>
        `;

        // Create iframe instead of webview
        const iframe = document.createElement('iframe');
        iframe.id = `iframe-${tabId}`;
        iframe.className = 'webview-iframe';
        iframe.style.cssText = `
            width: 100%;
            height: 100%;
            border: none;
            display: none;
            background: white;
        `;

        // Set iframe attributes for better compatibility
        iframe.setAttribute('sandbox', 'allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-presentation allow-top-navigation');
        iframe.setAttribute('allowfullscreen', 'true');
        iframe.setAttribute('webkitallowfullscreen', 'true');
        iframe.setAttribute('mozallowfullscreen', 'true');

        // Add iframe event handlers
        iframe.addEventListener('load', () => {
            console.log('Iframe loaded successfully:', url);
            const tabTitle = tab.querySelector('.tab-title');
            if (tabTitle) {
                try {
                    // Try to get the title from the iframe
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    const title = iframeDoc.title || new URL(url).hostname;
                    tabTitle.textContent = title.substring(0, 30) + (title.length > 30 ? '...' : '');
                } catch (e) {
                    // Cross-origin restrictions - use hostname
                    tabTitle.textContent = new URL(url).hostname;
                }
            }
        });

        iframe.addEventListener('error', (event) => {
            console.error('Iframe load error:', event);
            const tabTitle = tab.querySelector('.tab-title');
            if (tabTitle) {
                tabTitle.textContent = 'Load Error';
            }
        });

        // Add to containers
        webviewContainer.appendChild(iframe);
        tabsContainer.appendChild(tab);

        // Add to tabs array (using iframe as webview substitute)
        tabs.push({ id: tabId, webview: iframe, element: tab, isIframe: true });

        // Set iframe src
        iframe.src = url;

        console.log(`Iframe fallback created successfully for TabID: ${tabId}`);
        return iframe;

    } catch (error) {
        console.error('Error creating iframe fallback:', error);
        return null;
    }
}

// Function to safely destroy a webview
function destroyWebview(webview) {
    try {
        if (!webview) {
            console.log('No webview to destroy');
            return;
        }

        console.log('Destroying webview:', webview.id || 'unknown');

        // Clear any pending timeouts
        if (webview._unresponsiveTimeout) {
            clearTimeout(webview._unresponsiveTimeout);
            delete webview._unresponsiveTimeout;
        }

        // Clear retry counters to prevent memory leaks
        if (webview._webviewRetryCount) {
            delete webview._webviewRetryCount;
        }
        if (webview._retryCount) {
            delete webview._retryCount;
        }
        if (webview._guestViewRetryCount) {
            delete webview._guestViewRetryCount;
        }

        // Stop loading if still loading
        try {
            if (webview.isLoading && webview.isLoading()) {
                webview.stop();
            }
        } catch (stopErr) {
            console.warn('Error stopping webview loading:', stopErr);
        }

        // Remove from DOM
        if (webview.parentNode) {
            webview.parentNode.removeChild(webview);
        }

        console.log('Webview destroyed successfully');

    } catch (error) {
        console.error('Error destroying webview:', error);
    }
}

// Export functions
module.exports = {
    setupWebview,
    createWebview,
    createIframeFallback,
    destroyWebview
};