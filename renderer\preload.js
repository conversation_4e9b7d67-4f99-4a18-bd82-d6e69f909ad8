// This script runs in the context of the webview
const { blockImages, unblockImages } = require('./image-blocker.js');

// Helper function to check if domain is in whitelist
function isInWhitelist(domain, whitelist) {
  if (!domain || !whitelist || !whitelist.length) return false;

  const normalizedDomain = domain.toLowerCase().trim();

  return whitelist.some(whitelistDomain => {
    const normalizedWhitelistDomain = whitelistDomain.toLowerCase().trim();

    // Exact match
    if (normalizedDomain === normalizedWhitelistDomain) return true;

    // Subdomain match (e.g., sub.example.com matches example.com)
    if (normalizedDomain.endsWith('.' + normalizedWhitelistDomain)) return true;

    // www subdomain match (e.g., www.example.com matches example.com)
    if (normalizedDomain.startsWith('www.') && normalizedDomain.substring(4) === normalizedWhitelistDomain) return true;

    return false;
  });
}

// Function to check and apply image blocking/unblocking
function applyImageBlockingRules() {
  console.log('DOM READY EVENT: Checking image blocking settings');

  // Get current domain
  const currentDomain = window.location.hostname;
  console.log('DOM READY EVENT: Domain:', currentDomain);

  // Get settings from localStorage
  const blockImagesEnabled = localStorage.getItem('blockImages') === 'true';
  const imageWhitelist = JSON.parse(localStorage.getItem('imageWhitelist') || '[]');

  console.log('DOM READY EVENT: Image blocking enabled:', blockImagesEnabled);
  console.log('DOM READY EVENT: Whitelist:', imageWhitelist);
  console.log('DOM READY EVENT: Current domain is', currentDomain);

  // Check if domain is whitelisted using improved logic
  const isWhitelisted = isInWhitelist(currentDomain, imageWhitelist);
  console.log('DOM READY EVENT: Domain is whitelisted:', isWhitelisted);

  if (blockImagesEnabled && !isWhitelisted) {
    // Block images using our improved module
    console.log('Blocking images for:', currentDomain);
    blockImages();
  } else {
    // Explicitly unblock images if we're whitelisted or blocking is disabled
    console.log('Allowing images for:', currentDomain);
    unblockImages();
  }
}

// Listen for storage changes to update image blocking in real-time
window.addEventListener('storage', (event) => {
  if (event.key === 'blockImages' || event.key === 'imageWhitelist') {
    console.log('STORAGE EVENT: Image blocking settings changed, reapplying rules');
    applyImageBlockingRules();
  }
});

// Apply rules when DOM is ready
window.addEventListener("DOMContentLoaded", () => {
  applyImageBlockingRules();
});
