# Webview Crash and Error Handling Fixes

## Issues Addressed

### 1. GUEST_VIEW_MANAGER_CALL Errors
- **Problem**: Webviews were crashing with `GUEST_VIEW_MANAGER_CALL` errors, particularly when loading sites like AliExpress
- **Root Cause**: Poor webview lifecycle management and inadequate error handling during navigation and redirects

### 2. ERR_FAILED (-2) Loading Errors
- **Problem**: Sites failing to load with ERR_FAILED errors, especially during redirects
- **Root Cause**: Webview configuration conflicts and inadequate retry logic

### 3. ERR_ABORTED (-3) Redirect Issues
- **Problem**: Legitimate redirects (e.g., aliexpress.com → nl.aliexpress.com) being treated as intentional aborts
- **Root Cause**: Overly simplistic ERR_ABORTED handling that didn't distinguish between intentional aborts and failed redirects

### 4. Webview Crashes and Recovery
- **Problem**: Webviews crashing and not recovering properly, using deprecated crash events
- **Root Cause**: Using deprecated 'crashed' event instead of modern 'render-process-gone' event

## Fixes Implemented

### 1. Modern Crash Handling (main.js)
```javascript
// Replaced deprecated 'crashed' event with 'render-process-gone'
contents.on('render-process-gone', (event, details) => {
    console.log('Webview render process gone:', details.reason, 'exitCode:', details.exitCode);

    // Only reload for actual crashes, not normal exits
    if (details.reason === 'crashed' || details.reason === 'oom-killed') {
        // Add delay to prevent rapid reload loops
        setTimeout(() => {
            if (contents && !contents.isDestroyed()) {
                contents.reload();
            }
        }, 1000);
    }
});
```

### 2. Global Error Handlers (main.js)
```javascript
// Added global error handlers for GUEST_VIEW_MANAGER_CALL errors
process.on('uncaughtException', (error) => {
    if (error.message && error.message.includes('GUEST_VIEW_MANAGER_CALL')) {
        console.log('GUEST_VIEW_MANAGER_CALL error detected, continuing...');
    }
});
```

### 3. Intelligent ERR_ABORTED Handling (app.html)
```javascript
if (event.errorCode === -3) { // ERR_ABORTED
    // Check if this is a failed redirect between related domains
    const originalDomain = extractDomain(webview.src || '');
    const failedDomain = extractDomain(event.validatedURL || '');

    if (originalDomain && failedDomain &&
        (originalDomain.includes(failedDomain.split('.').slice(-2).join('.')) ||
         failedDomain.includes(originalDomain.split('.').slice(-2).join('.')))) {

        // Retry the failed redirect
        setTimeout(() => {
            webview.loadURL(event.validatedURL);
        }, 1500);
        return;
    }
}
```

### 4. Enhanced URL Loading with Recovery (app.html)
```javascript
webview.loadURL(url).catch(err => {
    const isGuestViewError = (err.message && err.message.includes('GUEST_VIEW_MANAGER_CALL'));

    if (isGuestViewError) {
        console.log('GUEST_VIEW_MANAGER_CALL error detected, attempting recovery...');
        setTimeout(() => {
            webview.loadURL(url); // Retry after delay
        }, 2000);
    }
});
```

### 5. Improved Webview Lifecycle Management (webview-handler.js)
```javascript
// Better webview creation with proper initialization order
function createWebview(url, tabId) {
    // Set up webview before setting src to prevent race conditions
    setupWebview(webview);

    // Add to container before setting src
    webviewContainer.appendChild(webview);

    // Set src last to trigger loading after everything is set up
    setTimeout(() => {
        webview.src = url;
    }, 100);
}
```

### 6. Enhanced Navigation Tracking (webview-handler.js)
```javascript
webview.addEventListener('did-redirect-navigation', (event) => {
    // Track redirects to help with error recovery
    if (event.isMainFrame) {
        webview._lastRedirectUrl = event.url;
    }
});
```

### 7. Safe Webview Destruction (webview-handler.js)
```javascript
function destroyWebview(webview) {
    // Clear timeouts, stop loading, remove from DOM safely
    if (webview._unresponsiveTimeout) {
        clearTimeout(webview._unresponsiveTimeout);
    }

    if (webview.isLoading && webview.isLoading()) {
        webview.stop();
    }

    if (webview.parentNode) {
        webview.parentNode.removeChild(webview);
    }
}
```

## Additional Fixes for Excessive Reloading

### 8. Retry Limiting System
```javascript
// Added retry counters to prevent infinite reload loops
if (!webview._retryCount) {
    webview._retryCount = {};
}

const retryKey = event.validatedURL || 'unknown';
const currentRetries = webview._retryCount[retryKey] || 0;

// Limit retries to prevent infinite loops
if (currentRetries >= 2) {
    console.log(`Retry limit reached for ${retryKey}, not retrying`);
    return;
}

webview._retryCount[retryKey] = currentRetries + 1;
```

### 9. Retry Counter Management
```javascript
// Clear retry counters on successful navigation
webview.addEventListener('dom-ready', () => {
    if (webview._retryCount) {
        console.log('Clearing retry counters after successful navigation');
        webview._retryCount = {};
    }
});

// Clear retry counters when webview is destroyed
function destroyWebview(webview) {
    if (webview._retryCount) {
        delete webview._retryCount;
    }
}
```

### 10. Loading State Checks
```javascript
// Only retry if webview is not currently loading
if (webview && document.body.contains(webview) && !webview.isLoading()) {
    webview.loadURL(event.validatedURL);
}
```

### 11. Enhanced Webview Validity Checks
```javascript
// Added robust webview validity checks before operations
if (webview && webview.src && document.body.contains(webview)) {
    webview.reload();
} else {
    console.log('Webview is destroyed or invalid, skipping reload');
}
```

### 12. Main Process Recovery Coordination
```javascript
// Main process now delegates recovery to renderer to prevent conflicts
contents.on('render-process-gone', (event, details) => {
    // Don't reload here - let the renderer-side webview-handler.js handle recovery
    // This prevents conflicts between main process and renderer process recovery attempts

    // Just notify the main window about the crash
    mainWindow.webContents.send('webview-crashed', {
        reason: details.reason,
        exitCode: details.exitCode
    });
});
```

## Expected Results

1. **Eliminated GUEST_VIEW_MANAGER_CALL Errors**: Proper webview validity checks and coordinated recovery prevent these errors
2. **Successful Redirect Handling**: Sites like AliExpress should now load properly through their redirects
3. **Improved Crash Recovery**: Webviews should recover more gracefully from crashes without conflicts
4. **Better Error Logging**: More detailed error information for debugging
5. **Reduced Memory Leaks**: Proper webview cleanup prevents memory accumulation
6. **No More Excessive Reloading**: Retry limiting prevents infinite reload loops
7. **Stable Navigation**: AliExpress and similar sites should load without constant reloading
8. **Coordinated Recovery**: Main and renderer processes no longer conflict during crash recovery

## Latest Fix: Removed Problematic Domain Blocking (December 2024)

### Problem
User requested removal of all problematic domain blocking to allow AliExpress, Chess.com, and all other websites to load normally within the Electron browser without being redirected to external browser.

### Solution
1. **Completely removed problematic domain lists** from all files
2. **Removed all external URL opening logic** for specific domains
3. **Eliminated all isProblematic() function calls** and domain checks
4. **Preserved error handling improvements** to maintain stability
5. **Maintained ERR_ABORTED error handling** without domain-specific blocking

### Changes Made
- `webview-handler.js`: Removed problematic domain checks and added intelligent ERR_ABORTED retry logic
- `app.html`: Removed domain checks and improved ERR_ABORTED handling consistency
- `renderer.js`: Removed problematic domain lists and isProblematic function
- `renderer/renderer.js`: Removed problematic domain blocking from all navigation paths
- `preload.js`: Removed AliExpress special handling
- `main.js`: Previously removed webRequest interception (earlier fix)

### Additional Improvements for Complex Websites
1. **Enhanced webview configuration** with disabled web security for better compatibility
2. **Intelligent ERR_ABORTED retry logic** that detects legitimate redirect failures
3. **Improved user agent** for better website compatibility
4. **Shared partition usage** for consistent session handling
5. **Better error recovery** with retry counters and timeout handling

## Testing Recommendations

1. **NEW**: Test loading `https://aliexpress.com` and verify it loads WITHIN the webview (not external browser)
2. **NEW**: Test loading `https://chess.com` and verify it loads WITHIN the webview (not external browser)
3. Test webview crash recovery by loading memory-intensive sites
4. Monitor console logs for GUEST_VIEW_MANAGER_CALL errors (should be eliminated)
5. Test rapid navigation between different sites to verify stability
6. Test bookmark loading and new tab creation for stability
7. **NEW**: Test various AliExpress URLs (nl.aliexpress.com, etc.) to ensure they load internally
8. **NEW**: Verify no websites are automatically redirected to external browser

## Files Modified

- `main.js`: Updated crash handling and added global error handlers
- `app.html`: Enhanced ERR_ABORTED handling and URL loading recovery
- `webview-handler.js`: Improved webview lifecycle management and navigation tracking
